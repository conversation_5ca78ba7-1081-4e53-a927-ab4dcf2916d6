namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Threading
open System.Threading.Tasks
open Automation.Core
open Automation.Utilities.Logging

/// Load balancing and horizontal scaling for AI automation services
module LoadBalancer =
    
    /// Worker instance identifier
    type WorkerId = string
    
    /// Worker instance status
    type WorkerStatus = {
        Id: WorkerId
        Health: HealthStatus
        CurrentLoad: int
        MaxCapacity: int
        LastHeartbeat: DateTimeOffset
        ProcessingRate: float // tasks per second
        AvgResponseTime: TimeSpan
    }
    and HealthStatus = 
        | Healthy 
        | Degraded 
        | Unhealthy
    
    /// Load balancing strategy
    type LoadBalancingStrategy =
        | RoundRobin
        | LeastConnections
        | WeightedRoundRobin of weights: Map<WorkerId, int>
        | HealthAware
        | ResponseTimeOptimized
    
    /// Auto-scaling configuration
    type AutoScalingConfig = {
        MinInstances: int
        MaxInstances: int
        ScaleUpThreshold: float  // CPU/memory threshold for scaling up (0.0-1.0)
        ScaleDownThreshold: float // CPU/memory threshold for scaling down
        CooldownPeriodMinutes: int
        TargetCpuUtilization: float
        TargetMemoryUtilization: float
        ScaleUpStepSize: int
        ScaleDownStepSize: int
        EnableAutoScaling: bool
    }
    
    /// Load balancer configuration
    type LoadBalancerConfig = {
        Strategy: LoadBalancingStrategy
        HealthCheckIntervalSeconds: int
        WorkerTimeoutSeconds: int
        AutoScaling: AutoScalingConfig
        EnableMetrics: bool
        AuditLogger: AuditLogger.AuditLogger option
    }
    
    /// Default load balancer configuration
    let defaultLoadBalancerConfig = {
        Strategy = HealthAware
        HealthCheckIntervalSeconds = 30
        WorkerTimeoutSeconds = 120
        AutoScaling = {
            MinInstances = 2
            MaxInstances = 10
            ScaleUpThreshold = 0.8
            ScaleDownThreshold = 0.3
            CooldownPeriodMinutes = 5
            TargetCpuUtilization = 0.7
            TargetMemoryUtilization = 0.8
            ScaleUpStepSize = 2
            ScaleDownStepSize = 1
            EnableAutoScaling = true
        }
        EnableMetrics = true
        AuditLogger = None
    }
    
    /// Worker registry for tracking active workers
    type WorkerRegistry() =
        let workers = ConcurrentDictionary<WorkerId, WorkerStatus>()
        let roundRobinIndex = ref 0
        
        /// Register a new worker
        member _.RegisterWorker(workerId: WorkerId, maxCapacity: int) =
            let status = {
                Id = workerId
                Health = Healthy
                CurrentLoad = 0
                MaxCapacity = maxCapacity
                LastHeartbeat = DateTimeOffset.Now
                ProcessingRate = 0.0
                AvgResponseTime = TimeSpan.Zero
            }
            workers.AddOrUpdate(workerId, status, fun _ _ -> status) |> ignore
            info (sprintf "[LoadBalancer] Worker %s registered with capacity %d" workerId maxCapacity)
        
        /// Update worker status
        member _.UpdateWorkerStatus(workerId: WorkerId, health: HealthStatus, currentLoad: int, processingRate: float, avgResponseTime: TimeSpan) =
            workers.AddOrUpdate(workerId, 
                (fun _ -> failwith "Worker not found"),
                (fun _ status -> 
                    { status with 
                        Health = health
                        CurrentLoad = currentLoad
                        LastHeartbeat = DateTimeOffset.Now
                        ProcessingRate = processingRate
                        AvgResponseTime = avgResponseTime
                    })) |> ignore
        
        /// Remove worker
        member _.UnregisterWorker(workerId: WorkerId) =
            let removed = workers.TryRemove(workerId)
            if fst removed then
                info (sprintf "[LoadBalancer] Worker %s unregistered" workerId)
        
        /// Get all healthy workers
        member _.GetHealthyWorkers() =
            workers.Values 
            |> Seq.filter (fun w -> 
                w.Health = Healthy && 
                (DateTimeOffset.Now - w.LastHeartbeat).TotalSeconds < 120.0)
            |> Seq.toList
        
        /// Get worker by ID
        member _.GetWorker(workerId: WorkerId) =
            match workers.TryGetValue(workerId) with
            | true, worker -> Some worker
            | false, _ -> None
        
        /// Get all workers
        member _.GetAllWorkers() = workers.Values |> Seq.toList
        
        /// Select worker using round robin strategy
        member _.SelectWorkerRoundRobin() =
            let healthyWorkers = workers.Values |> Seq.filter (fun w -> w.Health = Healthy) |> Seq.toArray
            if healthyWorkers.Length = 0 then None
            else
                let index = Interlocked.Increment(roundRobinIndex) % healthyWorkers.Length
                Some healthyWorkers.[index]
        
        /// Select worker with least connections
        member _.SelectWorkerLeastConnections() =
            workers.Values 
            |> Seq.filter (fun w -> w.Health = Healthy)
            |> Seq.sortBy (fun w -> w.CurrentLoad)
            |> Seq.tryHead
        
        /// Select worker optimized for response time
        member _.SelectWorkerResponseTimeOptimized() =
            workers.Values 
            |> Seq.filter (fun w -> w.Health = Healthy && w.CurrentLoad < w.MaxCapacity)
            |> Seq.sortBy (fun w -> w.AvgResponseTime.TotalMilliseconds)
            |> Seq.tryHead
    
    /// Load balancer implementation
    type LoadBalancer(config: LoadBalancerConfig) =
        let registry = WorkerRegistry()
        let lastScalingAction = ref DateTimeOffset.MinValue
        let mutable isHealthCheckRunning = false
        
        /// Start health check background task
        member private _.StartHealthCheck() =
            if not isHealthCheckRunning then
                isHealthCheckRunning <- true
                Task.Run(fun () ->
                    async {
                        while isHealthCheckRunning do
                            try
                                let now = DateTimeOffset.Now
                                let timeoutThreshold = TimeSpan.FromSeconds(float config.WorkerTimeoutSeconds)
                                
                                // Mark workers as unhealthy if they haven't sent heartbeat
                                for worker in registry.GetAllWorkers() do
                                    if (now - worker.LastHeartbeat) > timeoutThreshold then
                                        registry.UpdateWorkerStatus(worker.Id, Unhealthy, worker.CurrentLoad, worker.ProcessingRate, worker.AvgResponseTime)
                                        warn (sprintf "[LoadBalancer] Worker %s marked as unhealthy (no heartbeat)" worker.Id)
                                
                                do! Async.Sleep (config.HealthCheckIntervalSeconds * 1000)
                            with
                            | ex -> error (sprintf "[LoadBalancer] Health check error: %s" ex.Message)
                    } |> Async.StartAsTask
                ) |> ignore
        
        /// Stop health check
        member private _.StopHealthCheck() =
            isHealthCheckRunning <- false
        
        /// Select best worker based on configured strategy
        member _.SelectWorker() =
            match config.Strategy with
            | RoundRobin -> registry.SelectWorkerRoundRobin()
            | LeastConnections -> registry.SelectWorkerLeastConnections()
            | ResponseTimeOptimized -> registry.SelectWorkerResponseTimeOptimized()
            | HealthAware -> 
                // Health-aware strategy: prefer workers with low load and good health
                registry.GetHealthyWorkers()
                |> List.sortBy (fun w -> 
                    let loadRatio = float w.CurrentLoad / float w.MaxCapacity
                    let responseTimePenalty = w.AvgResponseTime.TotalMilliseconds / 1000.0
                    loadRatio + responseTimePenalty)
                |> List.tryHead
            | WeightedRoundRobin weights ->
                // Implement weighted round robin (simplified)
                registry.SelectWorkerRoundRobin()
        
        /// Register a worker
        member _.RegisterWorker(workerId: WorkerId, maxCapacity: int) =
            registry.RegisterWorker(workerId, maxCapacity)
            _.StartHealthCheck()
        
        /// Update worker status (heartbeat)
        member _.UpdateWorkerStatus(workerId: WorkerId, health: HealthStatus, currentLoad: int, processingRate: float, avgResponseTime: TimeSpan) =
            registry.UpdateWorkerStatus(workerId, health, currentLoad, processingRate, avgResponseTime)
        
        /// Unregister worker
        member _.UnregisterWorker(workerId: WorkerId) =
            registry.UnregisterWorker(workerId)
        
        /// Get load balancer metrics
        member _.GetMetrics() =
            let workers = registry.GetAllWorkers()
            let healthyWorkers = workers |> List.filter (fun w -> w.Health = Healthy)
            
            Map.ofList [
                ("total_workers", box workers.Length)
                ("healthy_workers", box healthyWorkers.Length)
                ("total_capacity", box (workers |> List.sumBy (fun w -> w.MaxCapacity)))
                ("current_load", box (workers |> List.sumBy (fun w -> w.CurrentLoad)))
                ("avg_processing_rate", box (workers |> List.averageBy (fun w -> w.ProcessingRate)))
                ("avg_response_time_ms", box (workers |> List.averageBy (fun w -> w.AvgResponseTime.TotalMilliseconds)))
            ]
        
        /// Check if auto-scaling action is needed
        member _.CheckAutoScaling() =
            if not config.AutoScaling.EnableAutoScaling then []
            else
                let workers = registry.GetHealthyWorkers()
                let totalCapacity = workers |> List.sumBy (fun w -> w.MaxCapacity)
                let totalLoad = workers |> List.sumBy (fun w -> w.CurrentLoad)
                let loadRatio = if totalCapacity > 0 then float totalLoad / float totalCapacity else 0.0
                
                let now = DateTimeOffset.Now
                let cooldownPassed = (now - !lastScalingAction).TotalMinutes >= float config.AutoScaling.CooldownPeriodMinutes
                
                if cooldownPassed then
                    if loadRatio >= config.AutoScaling.ScaleUpThreshold && workers.Length < config.AutoScaling.MaxInstances then
                        lastScalingAction := now
                        let instancesToAdd = min config.AutoScaling.ScaleUpStepSize (config.AutoScaling.MaxInstances - workers.Length)
                        config.AuditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.Info,
                                AuditLogger.Info,
                                "LoadBalancer",
                                $"Auto-scaling up: adding {instancesToAdd} instances (load ratio: {loadRatio:F2})",
                                Map.ofList [
                                    ("action", box "scale_up")
                                    ("instances_to_add", box instancesToAdd)
                                    ("load_ratio", box loadRatio)
                                    ("current_instances", box workers.Length)
                                ]
                            )
                        )
                        [sprintf "scale_up:%d" instancesToAdd]
                    elif loadRatio <= config.AutoScaling.ScaleDownThreshold && workers.Length > config.AutoScaling.MinInstances then
                        lastScalingAction := now
                        let instancesToRemove = min config.AutoScaling.ScaleDownStepSize (workers.Length - config.AutoScaling.MinInstances)
                        config.AuditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.Info,
                                AuditLogger.Info,
                                "LoadBalancer",
                                $"Auto-scaling down: removing {instancesToRemove} instances (load ratio: {loadRatio:F2})",
                                Map.ofList [
                                    ("action", box "scale_down")
                                    ("instances_to_remove", box instancesToRemove)
                                    ("load_ratio", box loadRatio)
                                    ("current_instances", box workers.Length)
                                ]
                            )
                        )
                        [sprintf "scale_down:%d" instancesToRemove]
                    else []
                else []
        
        /// Get worker selection for task routing
        member _.RouteTask() =
            match _.SelectWorker() with
            | Some worker -> 
                info (sprintf "[LoadBalancer] Routing task to worker %s (load: %d/%d)" worker.Id worker.CurrentLoad worker.MaxCapacity)
                Some worker.Id
            | None -> 
                warn "[LoadBalancer] No healthy workers available for task routing"
                None
        
        interface IDisposable with
            member _.Dispose() = _.StopHealthCheck()
    
    /// Load balancer manager for coordinating multiple load balancers
    type LoadBalancerManager() =
        let loadBalancers = ConcurrentDictionary<string, LoadBalancer>()
        
        /// Get or create load balancer for a service
        member _.GetLoadBalancer(serviceId: string, ?config: LoadBalancerConfig) =
            let config = defaultArg config defaultLoadBalancerConfig
            loadBalancers.GetOrAdd(serviceId, fun _ -> new LoadBalancer(config))
        
        /// Get all load balancer metrics
        member _.GetAllMetrics() =
            loadBalancers.ToArray()
            |> Array.map (fun kvp -> (kvp.Key, kvp.Value.GetMetrics()))
            |> Map.ofArray
        
        /// Dispose all load balancers
        interface IDisposable with
            member _.Dispose() =
                for kvp in loadBalancers do
                    kvp.Value.Dispose()
                loadBalancers.Clear()
