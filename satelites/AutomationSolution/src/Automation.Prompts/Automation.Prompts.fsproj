<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <Nullable>enable</Nullable>
    <LangVersion>preview</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="PromptStore.fs" />
    <Compile Include="PromptCache.fs" />
    <Compile Include="CacheMetrics.fs" />
    <Compile Include="ActionCache.fs" />
    <Compile Include="CachedPromptStore.fs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Automation.Core\Automation.Core.fsproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
  </ItemGroup>
</Project>
