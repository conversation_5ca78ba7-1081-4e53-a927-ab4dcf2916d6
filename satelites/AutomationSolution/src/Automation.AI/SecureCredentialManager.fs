namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Security.Cryptography
open System.Text
open System.Text.Json
open System.Threading.Tasks

/// Secure credential management for external services
module SecureCredentialManager =
    
    /// Credential types
    type CredentialType =
        | APIKey
        | OAuth2Token
        | DatabaseConnection
        | Certificate
        | SSHKey
        | Custom of string

    /// Encrypted credential
    type EncryptedCredential = {
        Id: string
        Name: string
        Type: CredentialType
        EncryptedValue: byte[]
        Salt: byte[]
        CreatedAt: DateTimeOffset
        ExpiresAt: DateTimeOffset option
        Metadata: Map<string, string>
    }

    /// Credential access result
    type CredentialResult =
        | Success of value: string
        | NotFound of credentialId: string
        | Expired of credentialId: string * expiredAt: DateTimeOffset
        | AccessDenied of reason: string
        | Error of message: string

    /// Credential access policy
    type AccessPolicy = {
        AllowedServices: string list
        AllowedHours: (int * int) option // Start hour, End hour (24h format)
        MaxUsageCount: int option
        RequiresMFA: bool
        IPWhitelist: string list
        UserWhitelist: string list
    }

    /// Helper functions to convert CredentialResult to standard Result types
    module CredentialResultHelpers =
        let toUnitResult (credResult: CredentialResult) : Result<unit, string> =
            match credResult with
            | Success _ -> Ok()
            | NotFound id -> Error $"Credential not found: {id}"
            | Expired (id, expiredAt) -> Error $"Credential {id} expired at {expiredAt}"
            | AccessDenied reason -> Error $"Access denied: {reason}"
            | Error message -> Error message

        let toStringResult (credResult: CredentialResult) : Result<string, string> =
            match credResult with
            | Success value -> Ok value
            | NotFound id -> Error $"Credential not found: {id}"
            | Expired (id, expiredAt) -> Error $"Credential {id} expired at {expiredAt}"
            | AccessDenied reason -> Error $"Access denied: {reason}"
            | Error message -> Error message

        let toOptionResult (credResult: CredentialResult) : Result<SecureCredential option, string> =
            match credResult with
            | Success _ -> Ok None // This case needs to be handled differently based on context
            | NotFound _ -> Ok None
            | Expired (id, expiredAt) -> Error $"Credential {id} expired at {expiredAt}"
            | AccessDenied reason -> Error $"Access denied: {reason}"
            | Error message -> Error message

    /// Credential with policy
    type SecureCredential = {
        Credential: EncryptedCredential
        AccessPolicy: AccessPolicy
        UsageCount: int
        LastAccessed: DateTimeOffset option
        AccessHistory: (DateTimeOffset * string * string) list // Time, User, Service
    }

    /// Credential storage interface
    type ICredentialStorage =
        abstract member Store: SecureCredential -> Async<Result<unit, string>>
        abstract member Retrieve: credentialId: string -> Async<Result<SecureCredential option, string>>
        abstract member List: unit -> Async<Result<EncryptedCredential list, string>>
        abstract member Delete: credentialId: string -> Async<Result<unit, string>>
        abstract member UpdateAccessHistory: credentialId: string * accessTime: DateTimeOffset * userId: string * service: string -> Async<Result<unit, string>>

    /// In-memory credential storage (for development/testing)
    type InMemoryCredentialStorage() =
        let storage = ConcurrentDictionary<string, SecureCredential>()
        
        interface ICredentialStorage with
            member _.Store(credential: SecureCredential) =
                async {
                    storage.AddOrUpdate(credential.Credential.Id, credential, fun _ _ -> credential) |> ignore
                    return Ok()
                }
            
            member _.Retrieve(credentialId: string) =
                async {
                    match storage.TryGetValue(credentialId) with
                    | true, credential -> return Ok(Some credential)
                    | false, _ -> return Ok(None)
                }
            
            member _.List() =
                async {
                    let credentials = 
                        storage.Values
                        |> Seq.map (fun sc -> sc.Credential)
                        |> List.ofSeq
                    return Ok(credentials)
                }
            
            member _.Delete(credentialId: string) =
                async {
                    storage.TryRemove(credentialId) |> ignore
                    return Ok()
                }
            
            member _.UpdateAccessHistory(credentialId: string, accessTime: DateTimeOffset, userId: string, service: string) =
                async {
                    match storage.TryGetValue(credentialId) with
                    | true, credential ->
                        let updatedCredential = {
                            credential with
                                UsageCount = credential.UsageCount + 1
                                LastAccessed = Some accessTime
                                AccessHistory = (accessTime, userId, service) :: credential.AccessHistory
                        }
                        storage.AddOrUpdate(credentialId, updatedCredential, fun _ _ -> updatedCredential) |> ignore
                        return Ok()
                    | false, _ ->
                        return Error($"Credential not found: {credentialId}")
                }

    /// Encryption utilities
    module Encryption =
        
        /// Generate a random salt
        let generateSalt () =
            let salt = Array.zeroCreate 32
            use rng = RandomNumberGenerator.Create()
            rng.GetBytes(salt)
            salt
        
        /// Derive key from master password and salt
        let deriveKey (masterPassword: string) (salt: byte[]) =
            use pbkdf2 = new Rfc2898DeriveBytes(masterPassword, salt, 100000, HashAlgorithmName.SHA256)
            pbkdf2.GetBytes(32)
        
        /// Encrypt credential value
        let encrypt (masterPassword: string) (value: string) =
            let salt = generateSalt()
            let key = deriveKey masterPassword salt
            let valueBytes = Encoding.UTF8.GetBytes(value)
            
            use aes = Aes.Create()
            aes.Key <- key
            aes.GenerateIV()
            
            use encryptor = aes.CreateEncryptor()
            let encrypted = encryptor.TransformFinalBlock(valueBytes, 0, valueBytes.Length)
            
            // Combine IV and encrypted data
            let result = Array.zeroCreate (aes.IV.Length + encrypted.Length)
            Array.Copy(aes.IV, 0, result, 0, aes.IV.Length)
            Array.Copy(encrypted, 0, result, aes.IV.Length, encrypted.Length)
            
            (result, salt)
        
        /// Decrypt credential value
        let decrypt (masterPassword: string) (encryptedData: byte[]) (salt: byte[]) =
            try
                let key = deriveKey masterPassword salt
                
                use aes = Aes.Create()
                aes.Key <- key
                
                // Extract IV and encrypted data
                let iv = Array.zeroCreate 16
                let encrypted = Array.zeroCreate (encryptedData.Length - 16)
                Array.Copy(encryptedData, 0, iv, 0, 16)
                Array.Copy(encryptedData, 16, encrypted, 0, encrypted.Length)
                
                aes.IV <- iv
                
                use decryptor = aes.CreateDecryptor()
                let decrypted = decryptor.TransformFinalBlock(encrypted, 0, encrypted.Length)
                
                Ok(Encoding.UTF8.GetString(decrypted))
            with
            | ex -> Error($"Decryption failed: {ex.Message}")

    /// Credential manager
    type CredentialManager(storage: ICredentialStorage, masterPassword: string, auditLogger: AuditLogger.AuditLogger option) =
        
        let validateAccessPolicy (policy: AccessPolicy) (userId: string) (service: string) (ipAddress: string option) =
            // Check service whitelist
            if not policy.AllowedServices.IsEmpty && not (policy.AllowedServices |> List.contains service) then
                AccessDenied($"Service '{service}' not in allowed services list")
            // Check user whitelist
            elif not policy.UserWhitelist.IsEmpty && not (policy.UserWhitelist |> List.contains userId) then
                AccessDenied($"User '{userId}' not in allowed users list")
            // Check IP whitelist
            elif not policy.IPWhitelist.IsEmpty && 
                 (ipAddress.IsNone || not (policy.IPWhitelist |> List.contains ipAddress.Value)) then
                AccessDenied($"IP address not in whitelist")
            // Check time restrictions
            elif policy.AllowedHours.IsSome then
                let (startHour, endHour) = policy.AllowedHours.Value
                let currentHour = DateTime.Now.Hour
                if currentHour < startHour || currentHour > endHour then
                    AccessDenied($"Access denied outside allowed hours ({startHour}:00 - {endHour}:00)")
                else
                    Success("")
            else
                Success("")
        
        member _.StoreCredential(id: string, name: string, credentialType: CredentialType, value: string, accessPolicy: AccessPolicy, ?expiresAt: DateTimeOffset, ?metadata: Map<string, string>) =
            async {
                try
                    let (encryptedValue, salt) = Encryption.encrypt masterPassword value
                    
                    let credential = {
                        Id = id
                        Name = name
                        Type = credentialType
                        EncryptedValue = encryptedValue
                        Salt = salt
                        CreatedAt = DateTimeOffset.Now
                        ExpiresAt = expiresAt
                        Metadata = defaultArg metadata Map.empty
                    }
                    
                    let secureCredential = {
                        Credential = credential
                        AccessPolicy = accessPolicy
                        UsageCount = 0
                        LastAccessed = None
                        AccessHistory = []
                    }
                    
                    let! result = storage.Store(secureCredential)
                    
                    match result with
                    | Ok() ->
                        auditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.ConfigurationChange,
                                AuditLogger.Info,
                                "CredentialManager",
                                $"Credential stored: {name}",
                                Map.ofList [
                                    ("credentialId", box id)
                                    ("credentialType", box (credentialType.ToString()))
                                    ("hasExpiration", box expiresAt.IsSome)
                                ]
                            )
                        )
                        return Ok()
                    | Error(err) ->
                        auditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.Error,
                                AuditLogger.Error,
                                "CredentialManager",
                                $"Failed to store credential: {err}",
                                Map.ofList [("credentialId", box id)]
                            )
                        )
                        return Error(err)
                with
                | ex ->
                    return Error($"Failed to store credential: {ex.Message}")
            }
        
        member _.RetrieveCredential(credentialId: string, userId: string, service: string, ?ipAddress: string) =
            async {
                try
                    let! result = storage.Retrieve(credentialId)
                    
                    match result with
                    | Ok(Some secureCredential) ->
                        // Check if credential has expired
                        match secureCredential.Credential.ExpiresAt with
                        | Some expiry when DateTimeOffset.Now > expiry ->
                            auditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.SecurityViolation,
                                    AuditLogger.Warning,
                                    "CredentialManager",
                                    $"Attempted access to expired credential: {credentialId}",
                                    Map.ofList [
                                        ("credentialId", box credentialId)
                                        ("userId", box userId)
                                        ("service", box service)
                                        ("expiredAt", box expiry)
                                    ]
                                )
                            )
                            return Error $"Credential {credentialId} expired at {expiry}"
                        | _ ->
                            // Validate access policy
                            let policyResult = validateAccessPolicy secureCredential.AccessPolicy userId service ipAddress
                            
                            match policyResult with
                            | AccessDenied(reason) ->
                                auditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.SecurityViolation,
                                        AuditLogger.Warning,
                                        "CredentialManager",
                                        $"Access denied to credential: {reason}",
                                        Map.ofList [
                                            ("credentialId", box credentialId)
                                            ("userId", box userId)
                                            ("service", box service)
                                            ("reason", box reason)
                                        ]
                                    )
                                )
                                return Error $"Access denied: {reason}"
                            | _ ->
                                // Check usage limits
                                match secureCredential.AccessPolicy.MaxUsageCount with
                                | Some maxUsage when secureCredential.UsageCount >= maxUsage ->
                                    auditLogger |> Option.iter (fun logger ->
                                        logger.LogEvent(
                                            AuditLogger.SecurityViolation,
                                            AuditLogger.Warning,
                                            "CredentialManager",
                                            $"Usage limit exceeded for credential: {credentialId}",
                                            Map.ofList [
                                                ("credentialId", box credentialId)
                                                ("usageCount", box secureCredential.UsageCount)
                                                ("maxUsage", box maxUsage)
                                            ]
                                        )
                                    )
                                    return Error "Access denied: Usage limit exceeded"
                                | _ ->
                                    // Decrypt credential
                                    match Encryption.decrypt masterPassword secureCredential.Credential.EncryptedValue secureCredential.Credential.Salt with
                                    | Ok(decryptedValue) ->
                                        // Update access history
                                        let now = DateTimeOffset.Now
                                        let! updateResult = storage.UpdateAccessHistory(credentialId, now, userId, service)
                                        
                                        auditLogger |> Option.iter (fun logger ->
                                            logger.LogEvent(
                                                AuditLogger.ConfigurationChange,
                                                AuditLogger.Info,
                                                "CredentialManager",
                                                $"Credential accessed: {credentialId}",
                                                Map.ofList [
                                                    ("credentialId", box credentialId)
                                                    ("userId", box userId)
                                                    ("service", box service)
                                                    ("usageCount", box (secureCredential.UsageCount + 1))
                                                ]
                                            )
                                        )
                                        
                                        return Ok decryptedValue
                                    | Error(decryptError) ->
                                        auditLogger |> Option.iter (fun logger ->
                                            logger.LogEvent(
                                                AuditLogger.Error,
                                                AuditLogger.Error,
                                                "CredentialManager",
                                                $"Failed to decrypt credential: {credentialId}",
                                                Map.ofList [("error", box decryptError)]
                                            )
                                        )
                                        return Error($"Decryption failed: {decryptError}")
                    | Ok(None) ->
                        auditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.SecurityViolation,
                                AuditLogger.Warning,
                                "CredentialManager",
                                $"Attempted access to non-existent credential: {credentialId}",
                                Map.ofList [
                                    ("credentialId", box credentialId)
                                    ("userId", box userId)
                                    ("service", box service)
                                ]
                            )
                        )
                        return Error $"Credential not found: {credentialId}"
                    | Error(err) ->
                        return Error(err)
                with
                | ex ->
                    return Error($"Failed to retrieve credential: {ex.Message}")
            }
        
        member _.ListCredentials() =
            async {
                let! result = storage.List()
                
                match result with
                | Ok(credentials) ->
                    // Return only non-sensitive metadata
                    let credentialInfo = 
                        credentials
                        |> List.map (fun c -> {|
                            Id = c.Id
                            Name = c.Name
                            Type = c.Type.ToString()
                            CreatedAt = c.CreatedAt
                            ExpiresAt = c.ExpiresAt
                            HasExpired = c.ExpiresAt.IsSome && DateTimeOffset.Now > c.ExpiresAt.Value
                        |})
                    return Ok(credentialInfo)
                | Error(err) ->
                    return Error(err)
            }
        
        member _.DeleteCredential(credentialId: string, userId: string) =
            async {
                let! result = storage.Delete(credentialId)
                
                match result with
                | Ok() ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.ConfigurationChange,
                            AuditLogger.Warning,
                            "CredentialManager",
                            $"Credential deleted: {credentialId}",
                            Map.ofList [
                                ("credentialId", box credentialId)
                                ("deletedBy", box userId)
                            ]
                        )
                    )
                    return Ok()
                | Error(err) ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Error,
                            AuditLogger.Error,
                            "CredentialManager",
                            $"Failed to delete credential: {err}",
                            Map.ofList [("credentialId", box credentialId)]
                        )
                    )
                    return Error(err)
            }
        
        member _.RotateCredential(credentialId: string, newValue: string, userId: string) =
            async {
                let! retrieveResult = storage.Retrieve(credentialId)
                
                match retrieveResult with
                | Ok(Some secureCredential) ->
                    let (encryptedValue, salt) = Encryption.encrypt masterPassword newValue
                    
                    let updatedCredential = {
                        secureCredential.Credential with
                            EncryptedValue = encryptedValue
                            Salt = salt
                            CreatedAt = DateTimeOffset.Now
                    }
                    
                    let updatedSecureCredential = {
                        secureCredential with
                            Credential = updatedCredential
                            UsageCount = 0  // Reset usage count after rotation
                            AccessHistory = []  // Clear access history after rotation
                    }
                    
                    let! storeResult = storage.Store(updatedSecureCredential)
                    
                    match storeResult with
                    | Ok() ->
                        auditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.ConfigurationChange,
                                AuditLogger.Info,
                                "CredentialManager",
                                $"Credential rotated: {credentialId}",
                                Map.ofList [
                                    ("credentialId", box credentialId)
                                    ("rotatedBy", box userId)
                                ]
                            )
                        )
                        return Ok()
                    | Error(err) ->
                        return Error(err)
                | Ok(None) ->
                    return Error($"Credential not found: {credentialId}")
                | Error(err) ->
                    return Error(err)
            }

    /// Default access policy for low-risk credentials
    let defaultAccessPolicy = {
        AllowedServices = []  // Empty means all services allowed
        AllowedHours = None   // No time restrictions
        MaxUsageCount = None  // No usage limits
        RequiresMFA = false
        IPWhitelist = []      // Empty means all IPs allowed
        UserWhitelist = []    // Empty means all users allowed
    }

    /// Restrictive access policy for high-risk credentials
    let restrictiveAccessPolicy = {
        AllowedServices = []  // Must be explicitly configured
        AllowedHours = Some(9, 17)  // Business hours only
        MaxUsageCount = Some(100)   // Limited usage
        RequiresMFA = true
        IPWhitelist = []      // Must be explicitly configured
        UserWhitelist = []    // Must be explicitly configured
    }

    /// Create in-memory credential manager (for development/testing)
    let createInMemoryCredentialManager (masterPassword: string) (auditLogger: AuditLogger.AuditLogger option) =
        let storage = InMemoryCredentialStorage() :> ICredentialStorage
        new CredentialManager(storage, masterPassword, auditLogger)