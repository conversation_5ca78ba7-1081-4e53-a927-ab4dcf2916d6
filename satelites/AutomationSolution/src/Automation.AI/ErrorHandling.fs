namespace Automation.AI

open System
open System.Threading
open System.Threading.Tasks
open System.Collections.Concurrent

/// Comprehensive error handling and recovery mechanisms for production AI system
module ErrorHandling =
    
    /// Custom error types for AI operations
    type AIError =
        | ProviderUnavailable of providerId: string * reason: string
        | RateLimitExceeded of providerId: string * retryAfter: TimeSpan
        | InvalidInput of validationErrors: string list
        | TokenLimitExceeded of requested: int * maximum: int
        | AuthenticationFailed of providerId: string
        | ModelNotFound of model: string * providerId: string
        | TimeoutError of operation: string * timeoutMs: int
        | SecurityViolation of violation: string
        | InsufficientCredits of providerId: string
        | UnexpectedError of message: string * innerException: Exception option

    /// Error severity levels
    type ErrorSeverity =
        | Info      // Expected errors that can be handled gracefully
        | Warning   // Errors that might indicate issues but don't break functionality
        | Error     // Errors that prevent operation completion but system remains stable
        | Critical  // Errors that might compromise system stability

    /// Recovery strategy types
    type RecoveryStrategy<'a> =
        | Retry of maxAttempts: int * backoffMs: int
        | Fallback of alternativeAction: (unit -> Async<Result<'a, AIError>>)
        | CircuitBreaker of failureThreshold: int * timeoutMs: int
        | Graceful of defaultValue: 'a
        | Manual of escalationMessage: string

    /// Error handling configuration
    type ErrorConfig = {
        DefaultRetryAttempts: int
        DefaultBackoffMs: int
        CircuitBreakerThreshold: int
        CircuitBreakerTimeoutMs: int
        EnableDetailedLogging: bool
        ErrorAggregationWindowMs: int
        MaxErrorsPerWindow: int
    }

    /// Default error configuration
    let defaultErrorConfig = {
        DefaultRetryAttempts = 3
        DefaultBackoffMs = 1000
        CircuitBreakerThreshold = 5
        CircuitBreakerTimeoutMs = 30000
        EnableDetailedLogging = true
        ErrorAggregationWindowMs = 60000
        MaxErrorsPerWindow = 10
    }

    /// Circuit breaker state
    type CircuitBreakerState =
        | Closed
        | Open of openedAt: DateTimeOffset
        | HalfOpen

    /// Circuit breaker implementation
    type CircuitBreaker(name: string, failureThreshold: int, timeoutMs: int, auditLogger: AuditLogger.AuditLogger option) =
        let mutable state = Closed
        let mutable failureCount = 0
        let mutable lastFailureTime = DateTimeOffset.MinValue
        let lockObj = obj()
        
        member _.Execute<'T>(operation: unit -> Async<Result<'T, AIError>>) =
            async {
                lock lockObj (fun () ->
                    match state with
                    | Open(openedAt) when DateTimeOffset.Now - openedAt > TimeSpan.FromMilliseconds(float timeoutMs) ->
                        state <- HalfOpen
                        failureCount <- 0
                    | _ -> ()
                )
                
                match state with
                | Open(_) ->
                    return Error(ProviderUnavailable(name, "Circuit breaker is open"))
                | _ ->
                    try
                        let! result = operation()
                        
                        match result with
                        | Ok(value) ->
                            lock lockObj (fun () ->
                                if state = HalfOpen then
                                    state <- Closed
                                    auditLogger |> Option.iter (fun logger ->
                                        logger.LogEvent(
                                            AuditLogger.AuditEventType.HealthCheck,
                                            AuditLogger.AuditSeverity.Info,
                                            "CircuitBreaker",
                                            $"Circuit breaker {name} closed after successful operation",
                                            Map.ofList [("circuitBreakerName", box name)]
                                        )
                                    )
                                failureCount <- 0
                            )
                            return Ok(value)
                        | Error(error) ->
                            lock lockObj (fun () ->
                                failureCount <- failureCount + 1
                                lastFailureTime <- DateTimeOffset.Now
                                
                                if failureCount >= failureThreshold then
                                    state <- Open(DateTimeOffset.Now)
                                    auditLogger |> Option.iter (fun logger ->
                                        logger.LogEvent(
                                            AuditLogger.AuditEventType.Error,
                                            AuditLogger.AuditSeverity.Error,
                                            "CircuitBreaker",
                                            $"Circuit breaker {name} opened due to failure threshold",
                                            Map.ofList [
                                                ("circuitBreakerName", box name)
                                                ("failureCount", box failureCount)
                                                ("threshold", box failureThreshold)
                                            ]
                                        )
                                    )
                            )
                            return Error(error)
                    with
                    | ex ->
                        lock lockObj (fun () ->
                            failureCount <- failureCount + 1
                            lastFailureTime <- DateTimeOffset.Now
                            
                            if failureCount >= failureThreshold then
                                state <- Open(DateTimeOffset.Now)
                        )
                        return Error(UnexpectedError(ex.Message, Some(ex)))
            }
        
        member _.GetState() = state
        member _.GetFailureCount() = failureCount

    /// Retry mechanism with exponential backoff
    module RetryMechanism =
        
        let private calculateBackoff (attempt: int) (baseBackoffMs: int) =
            let exponentialBackoff = baseBackoffMs * (pown 2 (attempt - 1))
            let jitter = Random().Next(0, exponentialBackoff / 10) // Add 10% jitter
            TimeSpan.FromMilliseconds(float (exponentialBackoff + jitter))
        
        let retryWithBackoff<'T> (maxAttempts: int) (baseBackoffMs: int) (operation: int -> Async<Result<'T, AIError>>) (auditLogger: AuditLogger.AuditLogger option) =
            let rec attempt currentAttempt =
                async {
                    try
                        let! result = operation currentAttempt
                        
                        match result with
                        | Ok(value) ->
                            if currentAttempt > 1 then
                                auditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.AuditEventType.HealthCheck,
                                        AuditLogger.AuditSeverity.Info,
                                        "RetryMechanism",
                                        $"Operation succeeded after {currentAttempt} attempts",
                                        Map.ofList [("attemptNumber", box currentAttempt)]
                                    )
                                )
                            return Ok(value)
                        | Error(error) when currentAttempt < maxAttempts ->
                            let shouldRetry = 
                                match error with
                                | ProviderUnavailable(_, _) -> true
                                | TimeoutError(_, _) -> true
                                | RateLimitExceeded(_, _) -> true
                                | UnexpectedError(_, _) -> true
                                | _ -> false
                            
                            if shouldRetry then
                                let backoff = calculateBackoff currentAttempt baseBackoffMs
                                
                                auditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.AuditEventType.Error,
                                        AuditLogger.AuditSeverity.Warning,
                                        "RetryMechanism",
                                        $"Operation failed, retrying in {backoff.TotalSeconds}s (attempt {currentAttempt}/{maxAttempts})",
                                        Map.ofList [
                                            ("attemptNumber", box currentAttempt)
                                            ("maxAttempts", box maxAttempts)
                                            ("backoffMs", box backoff.TotalMilliseconds)
                                            ("error", box (error.ToString()))
                                        ]
                                    )
                                )
                                
                                do! Async.Sleep(int backoff.TotalMilliseconds)
                                return! attempt (currentAttempt + 1)
                            else
                                return Error(error)
                        | Error(error) ->
                            auditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.AuditEventType.Error,
                                    AuditLogger.AuditSeverity.Error,
                                    "RetryMechanism",
                                    $"Operation failed after {maxAttempts} attempts",
                                    Map.ofList [
                                        ("maxAttempts", box maxAttempts)
                                        ("finalError", box (error.ToString()))
                                    ]
                                )
                            )
                            return Result.Error(error)
                    with
                    | ex ->
                        let error = UnexpectedError(ex.Message, Some(ex))
                        if currentAttempt < maxAttempts then
                            let backoff = calculateBackoff currentAttempt baseBackoffMs
                            do! Async.Sleep(int backoff.TotalMilliseconds)
                            return! attempt (currentAttempt + 1)
                        else
                            return Result.Error(error)
                }
            
            attempt 1

    /// Dead letter queue for failed operations
    type DeadLetterQueue<'T>() =
        let queue = ConcurrentQueue<DateTimeOffset * 'T * AIError>()
        let maxItems = 1000
        
        member _.Enqueue(item: 'T, error: AIError) =
            queue.Enqueue((DateTimeOffset.Now, item, error))
            
            // Remove old items if queue gets too large
            if queue.Count > maxItems then
                let mutable removed = false
                while not removed && queue.Count > maxItems do
                    queue.TryDequeue() |> ignore
        
        member _.TryDequeue() =
            match queue.TryDequeue() with
            | true, (timestamp, item, error) -> Some(timestamp, item, error)
            | false, _ -> None
        
        member _.Count = queue.Count
        
        member _.GetItems() =
            queue.ToArray() |> Array.toList

    /// Error aggregation and analysis
    type ErrorAggregator(windowMs: int, maxErrorsPerWindow: int) =
        let errors = ConcurrentQueue<DateTimeOffset * AIError>()
        
        member _.RecordError(error: AIError) =
            let now = DateTimeOffset.Now
            errors.Enqueue((now, error))
            
            // Clean up old errors outside the window
            let cutoff = now.AddMilliseconds(float -windowMs)
            let mutable cleaned = false
            while not cleaned do
                match errors.TryPeek() with
                | true, (timestamp, _) when timestamp < cutoff ->
                    errors.TryDequeue() |> ignore
                | _ -> cleaned <- true
        
        member _.GetErrorRate() =
            let now = DateTimeOffset.Now
            let cutoff = now.AddMilliseconds(float -windowMs)
            
            errors.ToArray()
            |> Array.filter (fun (timestamp, _) -> timestamp >= cutoff)
            |> Array.length
        
        member private errorHandler.IsRateLimitExceeded() =
            errorHandler.GetErrorRate() >= maxErrorsPerWindow
        
        member _.GetRecentErrors() =
            let now = DateTimeOffset.Now
            let cutoff = now.AddMilliseconds(float -windowMs)
            
            errors.ToArray()
            |> Array.filter (fun (timestamp, _) -> timestamp >= cutoff)
            |> Array.map snd
            |> Array.toList

    /// Main error handler
    type ErrorHandler(config: ErrorConfig, auditLogger: AuditLogger.AuditLogger option) =
        let circuitBreakers = ConcurrentDictionary<string, CircuitBreaker>()
        let deadLetterQueues = ConcurrentDictionary<string, DeadLetterQueue<obj>>()
        let errorAggregator = ErrorAggregator(config.ErrorAggregationWindowMs, config.MaxErrorsPerWindow)
        
        member _.HandleError<'T>(operation: string, error: AIError, ?recovery: RecoveryStrategy<'T>) =
            async {
                // Record error for aggregation
                errorAggregator.RecordError(error)
                
                // Log error
                let severity = 
                    match error with
                    | SecurityViolation(_) -> AuditLogger.AuditSeverity.Critical
                    | AuthenticationFailed(_) -> AuditLogger.AuditSeverity.Error
                    | ProviderUnavailable(_, _) -> AuditLogger.AuditSeverity.Warning
                    | _ -> AuditLogger.AuditSeverity.Error
                
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.AuditEventType.Error,
                        severity,
                        "ErrorHandler",
                        $"Error in operation {operation}: {error}",
                        Map.ofList [
                            ("operation", box operation)
                            ("errorType", box (error.GetType().Name))
                            ("errorDetails", box (error.ToString()))
                        ]
                    )
                )
                
                // Apply recovery strategy if provided
                match recovery with
                | Some(Retry(maxAttempts, backoffMs)) ->
                    // This would be handled by the retry mechanism
                    return Error(error)
                | Some(Graceful(defaultValue)) ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.ActionExecution,
                            AuditLogger.AuditSeverity.Info,
                            "ErrorHandler",
                            $"Using graceful fallback for operation {operation}",
                            Map.ofList [("operation", box operation)]
                        )
                    )
                    return Ok(defaultValue)
                | Some(Manual(escalationMessage)) ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.AuditEventType.Error,
                            AuditLogger.AuditSeverity.Critical,
                            "ErrorHandler",
                            $"Manual intervention required: {escalationMessage}",
                            Map.ofList [
                                ("operation", box operation)
                                ("escalationMessage", box escalationMessage)
                            ]
                        )
                    )
                    return Error(error)
                | _ ->
                    return Error(error)
            }
        
        member _.GetOrCreateCircuitBreaker(name: string) =
            circuitBreakers.GetOrAdd(name, fun _ -> 
                CircuitBreaker(name, config.CircuitBreakerThreshold, config.CircuitBreakerTimeoutMs, auditLogger)
            )
        
        member private errorHandler.ExecuteWithErrorHandling<'T>(operation: string, action: unit -> Async<Result<'T, AIError>>, ?recovery: RecoveryStrategy<'T>) =
            async {
                try
                    // Check if we're in an error storm
                    if errorAggregator.IsRateLimitExceeded() then
                        auditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.AuditEventType.Error,
                                AuditLogger.AuditSeverity.Critical,
                                "ErrorHandler",
                                $"Error rate limit exceeded for operation {operation}",
                                Map.ofList [
                                    ("operation", box operation)
                                    ("errorRate", box (errorAggregator.GetErrorRate()))
                                    ("maxErrorsPerWindow", box config.MaxErrorsPerWindow)
                                ]
                            )
                        )
                        return Result.Error(RateLimitExceeded("ErrorHandler", TimeSpan.FromMilliseconds(float config.ErrorAggregationWindowMs)))
                    
                    let! result = action()
                    
                    match result with
                    | Ok(value) -> return Result.Ok(value)
                    | Error(error) ->
                        return! errorHandler.HandleError(operation, error, recovery)
                with
                | ex ->
                    let error = UnexpectedError(ex.Message, Some(ex))
                    return! errorHandler.HandleError(operation, error, recovery)
            }
        
        member _.ExecuteWithRetry<'T>(operation: string, action: int -> Async<Result<'T, AIError>>, ?maxAttempts: int, ?backoffMs: int) =
            let maxAttempts = defaultArg maxAttempts config.DefaultRetryAttempts
            let backoffMs = defaultArg backoffMs config.DefaultBackoffMs
            
            RetryMechanism.retryWithBackoff maxAttempts backoffMs action auditLogger
        
        member private errorHandler.ExecuteWithCircuitBreaker<'T>(circuitBreakerName: string, action: unit -> Async<Result<'T, AIError>>) =
            let circuitBreaker = errorHandler.GetOrCreateCircuitBreaker(circuitBreakerName)
            circuitBreaker.Execute(action)
        
        member _.GetErrorStatistics() =
            {|
                RecentErrorRate = errorAggregator.GetErrorRate()
                RecentErrors = errorAggregator.GetRecentErrors()
                CircuitBreakers = 
                    circuitBreakers
                    |> Seq.map (fun kvp -> {| Name = kvp.Key; State = kvp.Value.GetState(); FailureCount = kvp.Value.GetFailureCount() |})
                    |> List.ofSeq
                DeadLetterQueueSizes = 
                    deadLetterQueues
                    |> Seq.map (fun kvp -> {| Name = kvp.Key; Count = kvp.Value.Count |})
                    |> List.ofSeq
            |}

    /// Error mapping utilities
    module ErrorMapping =
        
        /// Convert exceptions to AIError
        let mapException (ex: Exception) =
            match ex with
            | :? TimeoutException as timeout ->
                TimeoutError("Operation", 30000)
            | :? UnauthorizedAccessException ->
                AuthenticationFailed("Unknown")
            | :? ArgumentException as arg ->
                InvalidInput([arg.Message])
            | _ ->
                UnexpectedError(ex.Message, Some(ex))
        
        /// Convert HTTP status codes to AIError
        let mapHttpStatusCode (statusCode: int) (content: string) =
            match statusCode with
            | 401 -> AuthenticationFailed("HTTP")
            | 403 -> SecurityViolation("Forbidden access")
            | 429 -> RateLimitExceeded("HTTP", TimeSpan.FromMinutes(1.0))
            | 500 | 502 | 503 | 504 -> ProviderUnavailable("HTTP", $"Server error: {statusCode}")
            | _ -> UnexpectedError($"HTTP {statusCode}: {content}", None)

    /// Helper functions for common error handling patterns
    module ErrorHelpers =
        
        /// Wrap async operation with error handling
        let wrapWithErrorHandling (errorHandler: ErrorHandler) (operation: string) (action: unit -> Async<'T>) =
            async {
                try
                    let! result = action()
                    return Ok(result)
                with
                | ex ->
                    let error = ErrorMapping.mapException ex
                    let! handledResult = errorHandler.HandleError(operation, error)
                    return handledResult
            }
        
        /// Execute with fallback value
        let executeWithFallback (action: unit -> Async<Result<'T, AIError>>) (fallbackValue: 'T) =
            async {
                let! result = action()
                match result with
                | Ok(value) -> return value
                | Error(_) -> return fallbackValue
            }
        
        /// Execute multiple operations and return first successful result
        let executeWithFallbacks (operations: (unit -> Async<Result<'T, AIError>>) list) =
            let rec tryOperations remaining =
                async {
                    match remaining with
                    | [] -> return Error(UnexpectedError("All fallback operations failed", None))
                    | op :: rest ->
                        let! result = op()
                        match result with
                        | Ok(value) -> return Ok(value)
                        | Error(_) -> return! tryOperations rest
                }
            tryOperations operations

    /// Create default error handler
    let createErrorHandler (config: ErrorConfig option) (auditLogger: AuditLogger.AuditLogger option) =
        let config = defaultArg config defaultErrorConfig
        new ErrorHandler(config, auditLogger)