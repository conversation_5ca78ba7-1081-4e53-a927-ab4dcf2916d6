namespace Automation.Prompts

open System
open Automation.Core
open System.IO
open System.Threading.Tasks
open Microsoft.EntityFrameworkCore

/// Prompt entity stored in SQLite (or any future EF provider)
[<CLIMutable>]
type Prompt = {
    Id : string
    Text : string
    UpdatedUtc : DateTime
}

/// Entity Framework Core DbContext for prompts
/// Flexible so that we can swap the provider (e.g. PostgreSQL) later without changing callers
/// Only a single table is needed
[<Sealed>]
type PromptsDbContext(options : DbContextOptions<PromptsDbContext>) =
    inherit DbContext(options)

    member this.Prompts = this.Set<Prompt>()

    override _.OnModelCreating(builder : ModelBuilder) =
        builder.Entity<Prompt>().HasKey([| "Id" |]) |> ignore
        base.OnModelCreating(builder)

module internal Config =
    /// Where we keep the database file by default ("~/Library/Application Support/automation_prompts/prompts.db" on macOS)
    let databasePath : string =
        let dir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "automation_prompts")
        if not (Directory.Exists(dir)) then Directory.CreateDirectory(dir) |> ignore
        Path.Combine(dir, "prompts.db")

module PromptStore =
    open Config

    // Build DbContextOptions once – SQLite for now
    let private options : DbContextOptions<PromptsDbContext> =
        let b = DbContextOptionsBuilder<PromptsDbContext>()
        b.UseSqlite($"Data Source={databasePath}") |> ignore
        b.Options

    /// Ensure the database and schema exist (light-weight for SQLite)
    let private ensureCreated () =
        use ctx = new PromptsDbContext(options)
        ctx.Database.EnsureCreated() |> ignore

    /// Get a prompt text by ID – returns None if not found
    let get (id : string) : Task<string option> = task {
        ensureCreated()
        use ctx = new PromptsDbContext(options)
        let! entity = ctx.Prompts.FindAsync(id)
        return Option.ofObj entity |> Option.map (fun e -> e.Text)
    }

    /// Insert or update a prompt
    let upsert (id : string) (text : string) : Task = task {
        ensureCreated()
        use ctx = new PromptsDbContext(options)
        let! entity = ctx.Prompts.FindAsync(id)
        let now = DateTime.UtcNow
        match Option.ofObj entity with
        | Some existingEntity ->
            let updated = { existingEntity with Text = text; UpdatedUtc = now }
            ctx.Prompts.Update(updated) |> ignore
        | None ->
            ctx.Prompts.Add({ Id = id; Text = text; UpdatedUtc = now }) |> ignore
        do! ctx.SaveChangesAsync() :> Task
    }

    /// Convenience: get or fallback to default literal
    let getOrDefault (id:string) (fallback:string) : Task<string> = task {
        let! res = get id
        return res |> Option.defaultValue fallback
    }
