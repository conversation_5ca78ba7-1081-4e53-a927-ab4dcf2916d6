namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Diagnostics
open System.Runtime
open System.Threading
open System.Threading.Tasks
open Automation.Core
open Automation.Utilities.Logging

/// Memory and CPU optimization for automation tasks, especially AI processing
module MemoryCpuOptimizer =
    
    /// Memory optimization configuration
    type MemoryOptimizationConfig = {
        EnableGarbageCollectionOptimization: bool
        GCLatencyMode: GCLatencyMode
        EnableLargeObjectHeapCompaction: bool
        MemoryThresholdMB: int // Trigger cleanup when memory usage exceeds this
        AutoCleanupIntervalMinutes: int
        EnableMemoryPressureMonitoring: bool
        MaxWorkingSetMB: int option // Soft limit for process memory
        EnableMemoryMapping: bool // For large data structures
        PoolStringAllocations: bool
        AuditLogger: AuditLogger.AuditLogger option
    }
    
    /// Default memory optimization configuration
    let defaultMemoryOptimizationConfig = {
        EnableGarbageCollectionOptimization = true
        GCLatencyMode = GCLatencyMode.Interactive
        EnableLargeObjectHeapCompaction = true
        MemoryThresholdMB = 400
        AutoCleanupIntervalMinutes = 10
        EnableMemoryPressureMonitoring = true
        MaxWorkingSetMB = Some 512
        EnableMemoryMapping = true
        PoolStringAllocations = true
        AuditLogger = None
    }
    
    /// CPU optimization configuration
    type CpuOptimizationConfig = {
        MaxConcurrency: int
        EnableProcessorAffinity: bool
        PreferredProcessors: int list option
        EnableWorkStealing: bool
        TaskSchedulerType: string // "Default", "ThreadPool", "Custom"
        CPUThrottleThreshold: float // 0.0-1.0, throttle when CPU usage exceeds this
        EnableCPUPressureMonitoring: bool
        OptimizeForLatency: bool // vs throughput
        EnableSIMDOptimizations: bool
        ParallelismDegree: int option // Override default parallelism
        AuditLogger: AuditLogger.AuditLogger option
    }
    
    /// Default CPU optimization configuration
    let defaultCpuOptimizationConfig = {
        MaxConcurrency = Environment.ProcessorCount
        EnableProcessorAffinity = false
        PreferredProcessors = None
        EnableWorkStealing = true
        TaskSchedulerType = "ThreadPool"
        CPUThrottleThreshold = 0.85
        EnableCPUPressureMonitoring = true
        OptimizeForLatency = true
        EnableSIMDOptimizations = true
        ParallelismDegree = None
        AuditLogger = None
    }
    
    /// Resource usage metrics
    type ResourceUsageMetrics = {
        MemoryUsageMB: float
        CPUUsagePercent: float
        GCCollections: int64 * int64 * int64 // Gen0, Gen1, Gen2
        GCMemoryMB: float
        ThreadCount: int
        HandleCount: int
        WorkingSetMB: float
        PrivateMemoryMB: float
        VirtualMemoryMB: float
        Timestamp: DateTimeOffset
    }
    
    /// Optimized data structure for pooling strings
    type StringPool(maxSize: int) =
        let pool = ConcurrentDictionary<string, bool>()
        let maxPoolSize = maxSize
        
        /// Get interned string or add to pool
        member _.GetOrIntern(str: string) =
            if String.IsNullOrEmpty(str) then
                str
            else
                if pool.Count < maxPoolSize then
                    pool.GetOrAdd(str, true) |> ignore
                    String.Intern(str)
                else
                    str
        
        /// Clear the pool
        member _.Clear() = pool.Clear()
        
        /// Get pool statistics
        member _.GetStats() = {| PoolSize = pool.Count; MaxSize = maxPoolSize |}
    
    /// Memory optimizer for managing memory usage
    type MemoryOptimizer(config: MemoryOptimizationConfig) =
        let stringPool = if config.PoolStringAllocations then Some (new StringPool(10000)) else None
        let cleanupTimer = new System.Timers.Timer(float (config.AutoCleanupIntervalMinutes * 60 * 1000))
        let mutable lastCleanupTime = DateTimeOffset.Now
        let mutable memoryPressureDetected = false
        
        do
            // Configure GC settings
            if config.EnableGarbageCollectionOptimization then
                GCSettings.LatencyMode <- config.GCLatencyMode
                
                if config.EnableLargeObjectHeapCompaction then
                    GCSettings.LargeObjectHeapCompactionMode <- GCLargeObjectHeapCompactionMode.CompactOnce
            
            cleanupTimer.Elapsed.Add(fun _ -> __.PerformScheduledCleanup() |> ignore)
            cleanupTimer.Start()
            
            config.AuditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.Info,
                    AuditLogger.Info,
                    "MemoryOptimizer",
                    "Memory optimizer initialized",
                    Map.ofList [
                        ("gcLatencyMode", box config.GCLatencyMode)
                        ("memoryThresholdMB", box config.MemoryThresholdMB)
                        ("enableStringPooling", box config.PoolStringAllocations)
                    ]
                )
            )
        
        /// Get current memory usage in MB
        member _.GetCurrentMemoryUsageMB() =
            let currentProcess = Process.GetCurrentProcess()
            float currentProcess.WorkingSet64 / (1024.0 * 1024.0)
        
        /// Check if memory threshold is exceeded
        member _.IsMemoryThresholdExceeded() =
            __.GetCurrentMemoryUsageMB() > float config.MemoryThresholdMB
        
        /// Perform memory cleanup
        member _.PerformMemoryCleanup(?force: bool) =
            async {
                let shouldCleanup = defaultArg force (__.IsMemoryThresholdExceeded())
                
                if shouldCleanup then
                    let beforeMemory = __.GetCurrentMemoryUsageMB()
                    
                    try
                        // Clear string pool if enabled
                        stringPool |> Option.iter (fun pool -> pool.Clear())
                        
                        // Force garbage collection
                        GC.Collect(2, GCCollectionMode.Forced, true)
                        GC.WaitForPendingFinalizers()
                        GC.Collect(2, GCCollectionMode.Forced, true)
                        
                        // Compact Large Object Heap if enabled
                        if config.EnableLargeObjectHeapCompaction then
                            GCSettings.LargeObjectHeapCompactionMode <- GCLargeObjectHeapCompactionMode.CompactOnce
                            GC.Collect()
                        
                        let afterMemory = __.GetCurrentMemoryUsageMB()
                        let memoryFreed = beforeMemory - afterMemory
                        
                        lastCleanupTime <- DateTimeOffset.Now
                        
                        config.AuditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.Info,
                                AuditLogger.Info,
                                "MemoryOptimizer",
                                $"Memory cleanup completed. Freed {memoryFreed:F2}MB",
                                Map.ofList [
                                    ("beforeMemoryMB", box beforeMemory)
                                    ("afterMemoryMB", box afterMemory)
                                    ("memoryFreedMB", box memoryFreed)
                                    ("isForced", box (defaultArg force false))
                                ]
                            )
                        )
                        
                        return memoryFreed
                    with
                    | ex ->
                        config.AuditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.Error,
                                AuditLogger.Error,
                                "MemoryOptimizer",
                                $"Error during memory cleanup: {ex.Message}",
                                Map.ofList [("exception", box ex.Message)]
                            )
                        )
                        
                        return 0.0
                else
                    return 0.0
            }
        
        /// Perform scheduled cleanup
        member private _.PerformScheduledCleanup() =
            async {
                if config.EnableMemoryPressureMonitoring then
                    let! _ = __.PerformMemoryCleanup(false)
                    return ()
            }
        
        /// Get string from pool (if enabled)
        member _.GetPooledString(str: string) =
            match stringPool with
            | Some pool -> pool.GetOrIntern(str)
            | None -> str
        
        /// Monitor memory pressure
        member _.MonitorMemoryPressure() =
            let currentMemory = __.GetCurrentMemoryUsageMB()
            let previousPressure = memoryPressureDetected
            
            memoryPressureDetected <- 
                match config.MaxWorkingSetMB with
                | Some maxMB -> currentMemory > float maxMB * 0.9 // 90% of max
                | None -> currentMemory > float config.MemoryThresholdMB
            
            if memoryPressureDetected && not previousPressure then
                config.AuditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Warning,
                        AuditLogger.Warning,
                        "MemoryOptimizer",
                        $"Memory pressure detected: {currentMemory:F2}MB",
                        Map.ofList [
                            ("currentMemoryMB", box currentMemory)
                            ("thresholdMB", box config.MemoryThresholdMB)
                        ]
                    )
                )
                
                // Trigger immediate cleanup
                __.PerformMemoryCleanup(true) |> Async.Start
            
            memoryPressureDetected
        
        /// Get memory statistics
        member _.GetMemoryStatistics() =
            let currentProcess = Process.GetCurrentProcess()
            let gen0 = GC.CollectionCount(0)
            let gen1 = GC.CollectionCount(1)
            let gen2 = GC.CollectionCount(2)
            let gcMemory = float (GC.GetTotalMemory(false)) / (1024.0 * 1024.0)

            {
                MemoryUsageMB = float currentProcess.WorkingSet64 / (1024.0 * 1024.0)
                CPUUsagePercent = 0.0 // Will be set by CPU optimizer
                GCCollections = (int64 gen0, int64 gen1, int64 gen2)
                GCMemoryMB = gcMemory
                ThreadCount = currentProcess.Threads.Count
                HandleCount = currentProcess.HandleCount
                WorkingSetMB = float currentProcess.WorkingSet64 / (1024.0 * 1024.0)
                PrivateMemoryMB = float currentProcess.PrivateMemorySize64 / (1024.0 * 1024.0)
                VirtualMemoryMB = float currentProcess.VirtualMemorySize64 / (1024.0 * 1024.0)
                Timestamp = DateTimeOffset.Now
            }
        
        /// Dispose resources
        interface IDisposable with
            member _.Dispose() =
                cleanupTimer.Stop()
                cleanupTimer.Dispose()
    
    /// CPU optimizer for managing CPU usage and concurrency
    type CpuOptimizer(config: CpuOptimizationConfig) =
        let mutable cpuUsagePercent = 0.0
        let perfCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total")
        let cpuMonitorTimer = new System.Timers.Timer(1000.0) // Monitor every second
        let mutable lastCpuTime = TimeSpan.Zero
        let mutable lastTime = DateTime.UtcNow
        let semaphore = new SemaphoreSlim(config.MaxConcurrency, config.MaxConcurrency)
        
        do
            // Set processor affinity if configured
            if config.EnableProcessorAffinity then
                match config.PreferredProcessors with
                | Some processors ->
                    let currentProcess = Process.GetCurrentProcess()
                    let affinityMask =
                        processors
                        |> List.fold (fun acc proc -> acc ||| (1 <<< proc)) 0
                    currentProcess.ProcessorAffinity <- IntPtr(affinityMask)
                | None -> ()
            
            // Configure parallelism
            match config.ParallelismDegree with
            | Some degree ->
                System.Threading.Tasks.TaskScheduler.Default.MaximumConcurrencyLevel <- degree
            | None -> ()
            
            cpuMonitorTimer.Elapsed.Add(fun _ -> __.UpdateCpuUsage())
            cpuMonitorTimer.Start()
            
            config.AuditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.Info,
                    AuditLogger.Info,
                    "CpuOptimizer",
                    "CPU optimizer initialized",
                    Map.ofList [
                        ("maxConcurrency", box config.MaxConcurrency)
                        ("cpuThrottleThreshold", box config.CPUThrottleThreshold)
                        ("optimizeForLatency", box config.OptimizeForLatency)
                    ]
                )
            )
        
        /// Update CPU usage measurement
        member private _.UpdateCpuUsage() =
            try
                let currentProcess = Process.GetCurrentProcess()
                let currentTime = DateTime.UtcNow
                let currentCpuTime = currentProcess.TotalProcessorTime
                
                if lastTime <> DateTime.MinValue then
                    let timeDiff = currentTime - lastTime
                    let cpuDiff = currentCpuTime - lastCpuTime
                    let usage = cpuDiff.TotalMilliseconds / timeDiff.TotalMilliseconds / float Environment.ProcessorCount * 100.0
                    cpuUsagePercent <- Math.Max(0.0, Math.Min(100.0, usage))
                
                lastTime <- currentTime
                lastCpuTime <- currentCpuTime
            with
            | _ -> () // Ignore errors in CPU monitoring
        
        /// Get current CPU usage percentage
        member _.GetCurrentCpuUsage() = cpuUsagePercent
        
        /// Check if CPU throttling should be applied
        member _.ShouldThrottleCpu() =
            cpuUsagePercent > (config.CPUThrottleThreshold * 100.0)
        
        /// Acquire CPU resource (with throttling)
        member _.AcquireCpuResourceAsync(?timeout: TimeSpan) =
            async {
                let timeoutSpan = defaultArg timeout (TimeSpan.FromSeconds(30.0))
                
                if __.ShouldThrottleCpu() then
                    // Add small delay when CPU is under pressure
                    do! Async.Sleep(10)
                
                let! acquired = semaphore.WaitAsync(timeoutSpan) |> Async.AwaitTask
                return acquired
            }
        
        /// Release CPU resource
        member _.ReleaseCpuResource() =
            semaphore.Release() |> ignore
        
        /// Execute task with CPU optimization
        member _.ExecuteOptimizedAsync<'T>(task: unit -> Async<'T>, ?priority: TaskCreationOptions) =
            async {
                let! acquired = __.AcquireCpuResourceAsync()
                if not acquired then
                    return Error "Failed to acquire CPU resource"
                else
                    try
                        let! result = task()
                        return Ok result
                    finally
                        __.ReleaseCpuResource()
            }
        
        /// Execute parallel tasks with optimization
        member _.ExecuteParallelOptimizedAsync<'T>(tasks: (unit -> Async<'T>) list, ?maxDegree: int) =
            async {
                let degree = defaultArg maxDegree (min config.MaxConcurrency tasks.Length)
                let semaphore = new SemaphoreSlim(degree, degree)
                
                let executeTask task = 
                    async {
                        let! acquired = semaphore.WaitAsync() |> Async.AwaitTask
                        if acquired then
                            try
                                let! result = task()
                                return Some result
                            finally
                                semaphore.Release() |> ignore
                        else
                            return None
                    }
                
                let! results = 
                    tasks 
                    |> List.map executeTask
                    |> Async.Parallel
                
                semaphore.Dispose()
                
                return results |> Array.choose id |> Array.toList
            }
        
        /// Get CPU statistics
        member _.GetCpuStatistics() =
            {
                MemoryUsageMB = 0.0 // Will be set by memory optimizer
                CPUUsagePercent = cpuUsagePercent
                GCCollections = (0L, 0L, 0L) // Will be set by memory optimizer
                GCMemoryMB = 0.0
                ThreadCount = Process.GetCurrentProcess().Threads.Count
                HandleCount = Process.GetCurrentProcess().HandleCount
                WorkingSetMB = 0.0
                PrivateMemoryMB = 0.0
                VirtualMemoryMB = 0.0
                Timestamp = DateTimeOffset.Now
            }
        
        /// Dispose resources
        interface IDisposable with
            member _.Dispose() =
                cpuMonitorTimer.Stop()
                cpuMonitorTimer.Dispose()
                perfCounter.Dispose()
                semaphore.Dispose()
    
    /// Combined memory and CPU optimizer
    type MemoryCpuOptimizer(memoryConfig: MemoryOptimizationConfig, cpuConfig: CpuOptimizationConfig) =
        let memoryOptimizer = new MemoryOptimizer(memoryConfig)
        let cpuOptimizer = new CpuOptimizer(cpuConfig)
        let metricsTimer = new System.Timers.Timer(5000.0) // Collect metrics every 5 seconds
        let metricsHistory = ConcurrentQueue<ResourceUsageMetrics>()
        let maxMetricsHistory = 720 // Keep 1 hour of metrics (5s intervals)
        
        do
            metricsTimer.Elapsed.Add(fun _ -> __.CollectMetrics())
            metricsTimer.Start()
        
        /// Collect comprehensive metrics
        member private _.CollectMetrics() =
            try
                let memoryStats = memoryOptimizer.GetMemoryStatistics()
                let cpuStats = cpuOptimizer.GetCpuStatistics()
                
                let combinedMetrics = {
                    memoryStats with CPUUsagePercent = cpuStats.CPUUsagePercent
                }
                
                metricsHistory.Enqueue(combinedMetrics)
                
                // Keep only recent metrics
                while metricsHistory.Count > maxMetricsHistory do
                    metricsHistory.TryDequeue() |> ignore
                
                // Monitor for pressure
                memoryOptimizer.MonitorMemoryPressure() |> ignore
                
            with
            | _ -> () // Ignore metrics collection errors
        
        /// Execute task with full optimization
        member _.ExecuteOptimizedTaskAsync<'T>(task: unit -> Async<'T>) =
            async {
                // Check memory pressure before starting
                let memoryPressure = memoryOptimizer.MonitorMemoryPressure()
                if memoryPressure then
                    let! _ = memoryOptimizer.PerformMemoryCleanup(false)
                    ()
                
                // Execute with CPU optimization
                return! cpuOptimizer.ExecuteOptimizedAsync(task)
            }
        
        /// Get pooled string (memory optimization)
        member _.GetPooledString(str: string) = memoryOptimizer.GetPooledString(str)
        
        /// Force memory cleanup
        member _.ForceMemoryCleanup() = memoryOptimizer.PerformMemoryCleanup(true)
        
        /// Get current resource metrics
        member _.GetCurrentMetrics() =
            let memoryStats = memoryOptimizer.GetMemoryStatistics()
            let cpuStats = cpuOptimizer.GetCpuStatistics()
            { memoryStats with CPUUsagePercent = cpuStats.CPUUsagePercent }
        
        /// Get metrics history
        member _.GetMetricsHistory(?minutes: int) =
            let minutesToGet = defaultArg minutes 60
            let pointsToGet = minutesToGet * 12 // 5-second intervals
            
            metricsHistory.ToArray()
            |> Array.rev
            |> Array.take (min pointsToGet metricsHistory.Count)
            |> Array.rev
            |> Array.toList
        
        /// Get resource usage summary
        member _.GetResourceSummary() =
            let recent = __.GetMetricsHistory(10) // Last 10 minutes
            if recent.IsEmpty then
                None
            else
                let avgMemory = recent |> List.averageBy (fun m -> m.MemoryUsageMB)
                let maxMemory = recent |> List.maxBy (fun m -> m.MemoryUsageMB) |> fun m -> m.MemoryUsageMB
                let avgCpu = recent |> List.averageBy (fun m -> m.CPUUsagePercent)
                let maxCpu = recent |> List.maxBy (fun m -> m.CPUUsagePercent) |> fun m -> m.CPUUsagePercent
                
                Some {|
                    AverageMemoryMB = avgMemory
                    MaxMemoryMB = maxMemory
                    AverageCpuPercent = avgCpu
                    MaxCpuPercent = maxCpu
                    SampleCount = recent.Length
                    TimeSpanMinutes = 10
                |}
        
        /// Dispose all resources
        interface IDisposable with
            member _.Dispose() =
                metricsTimer.Stop()
                metricsTimer.Dispose()
                memoryOptimizer.Dispose()
                cpuOptimizer.Dispose()
    
    /// Factory functions
    let createMemoryOptimizer (config: MemoryOptimizationConfig option) =
        let config = defaultArg config defaultMemoryOptimizationConfig
        new MemoryOptimizer(config)
    
    let createCpuOptimizer (config: CpuOptimizationConfig option) =
        let config = defaultArg config defaultCpuOptimizationConfig
        new CpuOptimizer(config)
    
    let createMemoryCpuOptimizer (memoryConfig: MemoryOptimizationConfig option) (cpuConfig: CpuOptimizationConfig option) =
        let memoryConfig = defaultArg memoryConfig defaultMemoryOptimizationConfig
        let cpuConfig = defaultArg cpuConfig defaultCpuOptimizationConfig
        new MemoryCpuOptimizer(memoryConfig, cpuConfig)