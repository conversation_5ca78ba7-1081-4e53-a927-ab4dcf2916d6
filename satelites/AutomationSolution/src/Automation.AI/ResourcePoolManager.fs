namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Threading
open System.Threading.Tasks
open System.Net.Http
open Automation.Core
open Automation.Utilities.Logging

/// Resource pooling and connection optimization for AI service interactions
module ResourcePoolManager =
    
    /// Resource pool configuration
    type ResourcePoolConfig = {
        MaxPoolSize: int
        MinPoolSize: int
        IdleTimeoutMinutes: int
        AcquisitionTimeoutSeconds: int
        ValidationIntervalMinutes: int
        PreWarmConnections: bool
        EnableMetrics: bool
        AuditLogger: AuditLogger.AuditLogger option
    }
    
    /// Default resource pool configuration
    let defaultResourcePoolConfig = {
        MaxPoolSize = 20
        MinPoolSize = 2
        IdleTimeoutMinutes = 10
        AcquisitionTimeoutSeconds = 30
        ValidationIntervalMinutes = 5
        PreWarmConnections = true
        EnableMetrics = true
        AuditLogger = None
    }
    
    /// HTTP client pool configuration
    type HttpClientPoolConfig = {
        BasePoolConfig: ResourcePoolConfig
        ConnectionTimeoutSeconds: int
        MaxResponseContentBufferSize: int64
        UserAgent: string
        EnableCompression: bool
        EnableCookieContainer: bool
        MaxConnectionsPerServer: int
    }
    
    /// Default HTTP client pool configuration
    let defaultHttpClientPoolConfig = {
        BasePoolConfig = defaultResourcePoolConfig
        ConnectionTimeoutSeconds = 30
        MaxResponseContentBufferSize = 1024L * 1024L * 10L // 10MB
        UserAgent = "AutomationAI/1.0"
        EnableCompression = true
        EnableCookieContainer = false
        MaxConnectionsPerServer = 10
    }
    
    /// Resource metrics
    type ResourceMetrics = {
        TotalCreated: int64
        TotalAcquired: int64
        TotalReleased: int64
        TotalDestroyed: int64
        CurrentActive: int
        CurrentIdle: int
        PoolSize: int
        AverageAcquisitionTime: TimeSpan
        AverageUtilizationTime: TimeSpan
        FailedAcquisitions: int64
        ValidationFailures: int64
    }
    
    /// Resource wrapper with metadata
    type PooledResource<'T> = {
        Resource: 'T
        Id: Guid
        CreatedAt: DateTimeOffset
        LastUsed: DateTimeOffset
        TotalUses: int
        IsValid: bool
    }
    
    /// Generic resource pool
    type ResourcePool<'T when 'T :> IDisposable>(
        config: ResourcePoolConfig,
        createResource: unit -> Async<'T>,
        validateResource: 'T -> Async<bool>,
        destroyResource: 'T -> Async<unit>) =
        
        let availableResources = ConcurrentQueue<PooledResource<'T>>()
        let activeResources = ConcurrentDictionary<Guid, PooledResource<'T>>()
        let semaphore = new SemaphoreSlim(config.MaxPoolSize, config.MaxPoolSize)
        
        let mutable totalCreated = 0L
        let mutable totalAcquired = 0L
        let mutable totalReleased = 0L
        let mutable totalDestroyed = 0L
        let mutable failedAcquisitions = 0L
        let mutable validationFailures = 0L
        
        let acquisitionTimes = ConcurrentQueue<TimeSpan>()
        let utilizationTimes = ConcurrentQueue<TimeSpan>()
        let maxMetricsSamples = 1000
        
        // Validation timer
        let validationTimer = new System.Timers.Timer(float (config.ValidationIntervalMinutes * 60 * 1000))
        
        let validateIdleResources() =
            async {
                try
                    let now = DateTimeOffset.Now
                    let toRemove = 
                        resources.Values
                        |> Seq.filter (fun r -> now.Subtract(r.LastUsed) > TimeSpan.FromMinutes(float config.MaxIdleTimeMinutes))
                        |> Seq.toList
                    
                    for resource in toRemove do
                        try
                            (resource.Resource :> IDisposable).Dispose()
                            resources.TryRemove(resource.Id) |> ignore
                        with
                        | ex -> 
                            config.AuditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.Error,
                                    AuditLogger.Warning,
                                    "ResourcePool",
                                    $"Error disposing idle resource: {ex.Message}",
                                    Map.ofList [("resourceId", box resource.Id)]
                                )
                            )
                with
                | ex ->
                    config.AuditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Error,
                            AuditLogger.Warning,
                            "ResourcePool",
                            $"Error during idle resource validation: {ex.Message}",
                            Map.empty
                        )
                    )
            }
        
        let preWarmPool() =
            async {
                try
                    let tasks = 
                        [1..config.MinPoolSize]
                        |> List.map (fun _ -> 
                            async {
                                try
                                    let! resource = config.ResourceFactory()
                                    let pooledResource = {
                                        Id = Guid.NewGuid().ToString()
                                        Resource = resource
                                        CreatedAt = DateTimeOffset.Now
                                        LastUsed = DateTimeOffset.Now
                                        InUse = false
                                    }
                                    resources.TryAdd(pooledResource.Id, pooledResource) |> ignore
                                with
                                | ex -> 
                                    config.AuditLogger |> Option.iter (fun logger ->
                                        logger.LogEvent(
                                            AuditLogger.Error,
                                            AuditLogger.Warning,
                                            "ResourcePool",
                                            $"Error pre-warming resource: {ex.Message}",
                                            Map.empty
                                        )
                                    )
                            }
                        )
                    
                    do! Async.Parallel tasks |> Async.Ignore
                with
                | ex ->
                    config.AuditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Error,
                            AuditLogger.Warning,
                            "ResourcePool",
                            $"Error during pool pre-warming: {ex.Message}",
                            Map.empty
                        )
                    )
            }
        
        do
            validationTimer.Elapsed.Add(fun _ -> validateIdleResources() |> Async.Start)
            validationTimer.Start()
            
            // Pre-warm connections if enabled
            if config.PreWarmConnections then
                preWarmPool() |> Async.Start
        
        /// Pre-warm the pool with minimum resources
        member private _.PreWarmPool() =
            async {
                try
                    let tasks = 
                        [1..config.MinPoolSize]
                        |> List.map (fun _ -> 
                            async {
                                let! resource = createResource()
                                let pooledResource = {
                                    Resource = resource
                                    Id = Guid.NewGuid()
                                    CreatedAt = DateTimeOffset.Now
                                    LastUsed = DateTimeOffset.Now
                                    TotalUses = 0
                                    IsValid = true
                                }
                                availableResources.Enqueue(pooledResource)
                                System.Threading.Interlocked.Increment(&totalCreated) |> ignore
                                
                                config.AuditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.Info,
                                        AuditLogger.Info,
                                        "ResourcePool",
                                        $"Pre-warmed resource {pooledResource.Id}",
                                        Map.ofList [
                                            ("resourceId", box pooledResource.Id)
                                            ("poolSize", box availableResources.Count)
                                        ]
                                    )
                                )
                            })
                    
                    do! Async.Parallel tasks |> Async.Ignore
                with
                | ex ->
                    config.AuditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Error,
                            AuditLogger.Error,
                            "ResourcePool",
                            $"Failed to pre-warm pool: {ex.Message}",
                            Map.ofList [("exception", box ex.Message)]
                        )
                    )
            }
        
        /// Acquire resource from pool
        member _.AcquireAsync(?timeout: TimeSpan) =
            async {
                let startTime = DateTimeOffset.Now
                let timeoutSpan = defaultArg timeout (TimeSpan.FromSeconds(float config.AcquisitionTimeoutSeconds))
                
                try
                    let! acquired = semaphore.WaitAsync(timeoutSpan) |> Async.AwaitTask
                    if not acquired then
                        System.Threading.Interlocked.Increment(&failedAcquisitions) |> ignore
                        return Error "Timeout acquiring resource from pool"
                    else
                        // Try to get existing resource
                        match availableResources.TryDequeue() with
                        | true, pooledResource when pooledResource.IsValid ->
                            let updatedResource = {
                                pooledResource with
                                    LastUsed = DateTimeOffset.Now
                                    TotalUses = pooledResource.TotalUses + 1
                            }
                            activeResources.[updatedResource.Id] <- updatedResource
                            System.Threading.Interlocked.Increment(&totalAcquired) |> ignore
                            
                            let acquisitionTime = DateTimeOffset.Now - startTime
                            __.RecordAcquisitionTime(acquisitionTime)
                            
                            config.AuditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.Info,
                                    AuditLogger.Info,
                                    "ResourcePool",
                                    $"Acquired existing resource {updatedResource.Id}",
                                    Map.ofList [
                                        ("resourceId", box updatedResource.Id)
                                        ("totalUses", box updatedResource.TotalUses)
                                        ("acquisitionTimeMs", box acquisitionTime.TotalMilliseconds)
                                    ]
                                )
                            )
                            
                            return Ok updatedResource.Resource
                        | _ ->
                            // Create new resource
                            try
                                let! newResource = createResource()
                                let pooledResource = {
                                    Resource = newResource
                                    Id = Guid.NewGuid()
                                    CreatedAt = DateTimeOffset.Now
                                    LastUsed = DateTimeOffset.Now
                                    TotalUses = 1
                                    IsValid = true
                                }
                                activeResources.[pooledResource.Id] <- pooledResource
                                System.Threading.Interlocked.Increment(&totalCreated) |> ignore
                                System.Threading.Interlocked.Increment(&totalAcquired) |> ignore
                                
                                let acquisitionTime = DateTimeOffset.Now - startTime
                                __.RecordAcquisitionTime(acquisitionTime)
                                
                                config.AuditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.Info,
                                        AuditLogger.Info,
                                        "ResourcePool",
                                        $"Created and acquired new resource {pooledResource.Id}",
                                        Map.ofList [
                                            ("resourceId", box pooledResource.Id)
                                            ("acquisitionTimeMs", box acquisitionTime.TotalMilliseconds)
                                        ]
                                    )
                                )
                                
                                return Ok newResource
                            with
                            | ex ->
                                semaphore.Release() |> ignore
                                System.Threading.Interlocked.Increment(&failedAcquisitions) |> ignore
                                
                                config.AuditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.Error,
                                        AuditLogger.Error,
                                        "ResourcePool",
                                        $"Failed to create new resource: {ex.Message}",
                                        Map.ofList [("exception", box ex.Message)]
                                    )
                                )
                                
                                return Error $"Failed to create resource: {ex.Message}"
                with
                | ex ->
                    System.Threading.Interlocked.Increment(&failedAcquisitions) |> ignore
                    return Error $"Failed to acquire resource: {ex.Message}"
            }
        
        /// Release resource back to pool
        member _.ReleaseAsync(resource: 'T) =
            async {
                let startTime = DateTimeOffset.Now
                
                try
                    // Find the resource in active pool
                    let resourceEntry = 
                        activeResources.ToArray()
                        |> Array.tryFind (fun kvp -> Object.ReferenceEquals(kvp.Value.Resource, resource))
                    
                    match resourceEntry with
                    | Some kvp ->
                        activeResources.TryRemove(kvp.Key) |> ignore
                        
                        let utilizationTime = DateTimeOffset.Now - kvp.Value.LastUsed
                        __.RecordUtilizationTime(utilizationTime)
                        
                        // Check if resource is still valid and not too old
                        let resourceAge = DateTimeOffset.Now - kvp.Value.CreatedAt
                        let idleTimeout = TimeSpan.FromMinutes(float config.IdleTimeoutMinutes)
                        
                        if resourceAge < idleTimeout then
                            try
                                let! isValid = validateResource resource
                                if isValid then
                                    let updatedResource = { kvp.Value with IsValid = true }
                                    availableResources.Enqueue(updatedResource)
                                    System.Threading.Interlocked.Increment(&totalReleased) |> ignore
                                    
                                    config.AuditLogger |> Option.iter (fun logger ->
                                        logger.LogEvent(
                                            AuditLogger.Info,
                                            AuditLogger.Info,
                                            "ResourcePool",
                                            $"Released resource {kvp.Key} back to pool",
                                            Map.ofList [
                                                ("resourceId", box kvp.Key)
                                                ("totalUses", box kvp.Value.TotalUses)
                                                ("utilizationTimeMs", box utilizationTime.TotalMilliseconds)
                                            ]
                                        )
                                    )
                                else
                                    do! destroyResource resource
                                    System.Threading.Interlocked.Increment(&totalDestroyed) |> ignore
                                    System.Threading.Interlocked.Increment(&validationFailures) |> ignore
                                    
                                    config.AuditLogger |> Option.iter (fun logger ->
                                        logger.LogEvent(
                                            AuditLogger.Warning,
                                            AuditLogger.Warning,
                                            "ResourcePool",
                                            $"Destroyed invalid resource {kvp.Key}",
                                            Map.ofList [("resourceId", box kvp.Key)]
                                        )
                                    )
                            with
                            | ex ->
                                do! destroyResource resource
                                System.Threading.Interlocked.Increment(&totalDestroyed) |> ignore
                                
                                config.AuditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.Error,
                                        AuditLogger.Error,
                                        "ResourcePool",
                                        $"Error validating resource {kvp.Key}, destroyed: {ex.Message}",
                                        Map.ofList [
                                            ("resourceId", box kvp.Key)
                                            ("exception", box ex.Message)
                                        ]
                                    )
                                )
                        else
                            do! destroyResource resource
                            System.Threading.Interlocked.Increment(&totalDestroyed) |> ignore
                            
                            config.AuditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.Info,
                                    AuditLogger.Info,
                                    "ResourcePool",
                                    $"Destroyed aged resource {kvp.Key}",
                                    Map.ofList [
                                        ("resourceId", box kvp.Key)
                                        ("ageMinutes", box resourceAge.TotalMinutes)
                                    ]
                                )
                            )
                        
                        semaphore.Release() |> ignore
                    | None ->
                        config.AuditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.Warning,
                                AuditLogger.Warning,
                                "ResourcePool",
                                "Attempted to release resource not found in active pool",
                                Map.empty
                            )
                        )
                with
                | ex ->
                    config.AuditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Error,
                            AuditLogger.Error,
                            "ResourcePool",
                            $"Error releasing resource: {ex.Message}",
                            Map.ofList [("exception", box ex.Message)]
                        )
                    )
            }
        
        /// Validate idle resources periodically
        member private _.ValidateIdleResources() =
            async {
                let idleResources = availableResources.ToArray()
                let now = DateTimeOffset.Now
                let idleTimeout = TimeSpan.FromMinutes(float config.IdleTimeoutMinutes)
                
                for resource in idleResources do
                    if now - resource.LastUsed > idleTimeout then
                        // Remove from available queue
                        let mutable temp = Unchecked.defaultof<PooledResource<'T>>
                        if availableResources.TryDequeue(&temp) && temp.Id = resource.Id then
                            try
                                do! destroyResource resource.Resource
                                System.Threading.Interlocked.Increment(&totalDestroyed) |> ignore
                                
                                config.AuditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.Info,
                                        AuditLogger.Info,
                                        "ResourcePool",
                                        $"Cleaned up idle resource {resource.Id}",
                                        Map.ofList [
                                            ("resourceId", box resource.Id)
                                            ("idleTimeMinutes", box (now - resource.LastUsed).TotalMinutes)
                                        ]
                                    )
                                )
                            with
                            | ex ->
                                config.AuditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.Error,
                                        AuditLogger.Error,
                                        "ResourcePool",
                                        $"Error cleaning up idle resource {resource.Id}: {ex.Message}",
                                        Map.ofList [
                                            ("resourceId", box resource.Id)
                                            ("exception", box ex.Message)
                                        ]
                                    )
                                )
            }
        
        /// Record acquisition time for metrics
        member private _.RecordAcquisitionTime(time: TimeSpan) =
            if config.EnableMetrics then
                acquisitionTimes.Enqueue(time)
                while acquisitionTimes.Count > maxMetricsSamples do
                    acquisitionTimes.TryDequeue() |> ignore
        
        /// Record utilization time for metrics
        member private _.RecordUtilizationTime(time: TimeSpan) =
            if config.EnableMetrics then
                utilizationTimes.Enqueue(time)
                while utilizationTimes.Count > maxMetricsSamples do
                    utilizationTimes.TryDequeue() |> ignore
        
        /// Get pool metrics
        member _.GetMetrics() =
            let avgAcquisitionTime = 
                let times = acquisitionTimes.ToArray()
                if times.Length > 0 then
                    TimeSpan.FromTicks(times |> Array.averageBy (fun t -> float t.Ticks) |> int64)
                else TimeSpan.Zero
            
            let avgUtilizationTime = 
                let times = utilizationTimes.ToArray()
                if times.Length > 0 then
                    TimeSpan.FromTicks(times |> Array.averageBy (fun t -> float t.Ticks) |> int64)
                else TimeSpan.Zero
            
            {
                TotalCreated = totalCreated
                TotalAcquired = totalAcquired
                TotalReleased = totalReleased
                TotalDestroyed = totalDestroyed
                CurrentActive = activeResources.Count
                CurrentIdle = availableResources.Count
                PoolSize = activeResources.Count + availableResources.Count
                AverageAcquisitionTime = avgAcquisitionTime
                AverageUtilizationTime = avgUtilizationTime
                FailedAcquisitions = failedAcquisitions
                ValidationFailures = validationFailures
            }
        
        /// Dispose all resources
        interface IDisposable with
            member _.Dispose() =
                validationTimer.Stop()
                validationTimer.Dispose()
                
                // Dispose all active resources
                for kvp in activeResources do
                    try
                        kvp.Value.Resource.Dispose()
                    with _ -> ()
                
                // Dispose all available resources
                let mutable resource = Unchecked.defaultof<PooledResource<'T>>
                while availableResources.TryDequeue(&resource) do
                    try
                        resource.Resource.Dispose()
                    with _ -> ()
                
                semaphore.Dispose()
    
    /// HTTP Client pool for AI service connections
    type HttpClientPool(config: HttpClientPoolConfig) =
        let createHttpClient () =
            async {
                let handler = new HttpClientHandler()
                handler.MaxConnectionsPerServer <- config.MaxConnectionsPerServer
                
                let client = new HttpClient(handler)
                client.Timeout <- TimeSpan.FromSeconds(float config.ConnectionTimeoutSeconds)
                client.MaxResponseContentBufferSize <- config.MaxResponseContentBufferSize
                client.DefaultRequestHeaders.Add("User-Agent", config.UserAgent)
                
                if config.EnableCompression then
                    handler.AutomaticDecompression <- 
                        System.Net.DecompressionMethods.GZip ||| System.Net.DecompressionMethods.Deflate
                
                return client
            }
        
        let validateHttpClient (client: HttpClient) =
            async {
                try
                    // Simple validation - check if client is not disposed by attempting to access timeout
                    return not (isNull client) && client.Timeout > TimeSpan.Zero
                with
                | _ -> return false
            }
        
        let destroyHttpClient (client: HttpClient) =
            async {
                try
                    client.Dispose()
                with _ -> ()
            }
        
        let pool = new ResourcePool<HttpClient>(
            config.BasePoolConfig,
            createHttpClient,
            validateHttpClient,
            destroyHttpClient)
        
        /// Get HTTP client from pool
        member _.GetClientAsync(?timeout: TimeSpan) =
            pool.AcquireAsync(?timeout = timeout)
        
        /// Return HTTP client to pool
        member _.ReturnClientAsync(client: HttpClient) =
            pool.ReleaseAsync(client)
        
        /// Get pool metrics
        member _.GetMetrics() = pool.GetMetrics()
        
        /// Dispose pool
        interface IDisposable with
            member _.Dispose() = 
                (pool :> IDisposable).Dispose()
    
    /// Resource pool manager for coordinating multiple pools
    type ResourcePoolManager() =
        let httpClientPools = ConcurrentDictionary<string, HttpClientPool>()
        
        /// Get or create HTTP client pool for provider
        member _.GetHttpClientPool(providerId: AIFramework.ProviderId, ?config: HttpClientPoolConfig) =
            let key = providerId.ToString()
            let config = defaultArg config defaultHttpClientPoolConfig
            
            httpClientPools.GetOrAdd(key, fun _ -> new HttpClientPool(config))
        
        /// Get all pool metrics
        member _.GetAllMetrics() =
            httpClientPools.ToArray()
            |> Array.map (fun kvp -> (kvp.Key, kvp.Value.GetMetrics()))
            |> Map.ofArray
        
        /// Dispose all pools
        interface IDisposable with
            member _.Dispose() =
                for kvp in httpClientPools do
                    try
                        (kvp.Value :> IDisposable).Dispose()
                    with _ -> ()
                httpClientPools.Clear()
    
    /// Factory functions
    let createResourcePool<'T when 'T :> IDisposable> (config: ResourcePoolConfig) (createResource: unit -> Async<'T>) (validateResource: 'T -> Async<bool>) (destroyResource: 'T -> Async<unit>) =
        new ResourcePool<'T>(config, createResource, validateResource, destroyResource)
    
    let createHttpClientPool (config: HttpClientPoolConfig option) =
        let config = defaultArg config defaultHttpClientPoolConfig
        new HttpClientPool(config)
    
    let createResourcePoolManager () =
        new ResourcePoolManager()