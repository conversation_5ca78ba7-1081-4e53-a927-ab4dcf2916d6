namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Collections.Generic
open Automation.Core
open Automation.AI.CostOptimizer

/// Advanced cost-aware provider selection algorithms with machine learning capabilities
module AdvancedCostAlgorithms =
    
    /// Historical performance data for a provider
    type ProviderPerformanceHistory = {
        ProviderId: AIFramework.ProviderId
        Model: string
        TimestampUtc: DateTimeOffset
        ActualCost: decimal
        EstimatedCost: decimal
        ActualLatency: TimeSpan
        EstimatedLatency: TimeSpan
        QualityScore: float option // User feedback or evaluation score
        SuccessRate: float // 0.0-1.0
        TokensUsed: int option
        ImagesProcessed: int option
        RequestType: string // "text", "vision", "complex_workflow", etc.
        UserSatisfaction: float option // 0.0-1.0 if available
    }
    
    /// Predictive model for cost and performance
    type PredictiveModel = {
        ModelId: string
        ModelType: ModelType
        TrainingData: ProviderPerformanceHistory list
        LastTrainingDate: DateTimeOffset
        AccuracyScore: float
        Parameters: Map<string, float>
        IsActive: bool
    }
    and ModelType = 
        | LinearRegression
        | WeightedAverage
        | TimeSeriesForecasting
        | NeuralNetwork
        | EnsembleModel of ModelType list
    
    /// Advanced selection algorithm configuration
    type AdvancedAlgorithmConfig = {
        EnableMachineLearning: bool
        HistoryWindowDays: int
        MinHistoricalDataPoints: int
        RetrainingIntervalHours: int
        PredictionConfidenceThreshold: float
        EnableSeasonalAdjustments: bool
        EnableDynamicWeighting: bool
        EnableABTesting: bool
        ABTestSampleSize: int
        OptimizationGoals: OptimizationGoal list
        FallbackToBasicSelection: bool
    }
    and OptimizationGoal = 
        | MinimizeCost
        | MaximizeQuality  
        | MinimizeLatency
        | MaximizeReliability
        | MaximizeUserSatisfaction
        | BalanceMultipleGoals of weights: Map<OptimizationGoal, float>
    
    /// Default advanced algorithm configuration
    let defaultAdvancedConfig = {
        EnableMachineLearning = true
        HistoryWindowDays = 30
        MinHistoricalDataPoints = 50
        RetrainingIntervalHours = 24
        PredictionConfidenceThreshold = 0.7
        EnableSeasonalAdjustments = true
        EnableDynamicWeighting = true
        EnableABTesting = false
        ABTestSampleSize = 100
        OptimizationGoals = [BalanceMultipleGoals(Map.ofList [
            (MinimizeCost, 0.3)
            (MaximizeQuality, 0.25)
            (MinimizeLatency, 0.2)
            (MaximizeReliability, 0.15)
            (MaximizeUserSatisfaction, 0.1)
        ])]
        FallbackToBasicSelection = true
    }
    
    /// Advanced cost optimizer with ML capabilities
    type AdvancedCostOptimizer(config: AdvancedAlgorithmConfig, basicOptimizer: CostOptimizer, auditLogger: AuditLogger.AuditLogger option) =
        let performanceHistory = ConcurrentQueue<ProviderPerformanceHistory>()
        let models = ConcurrentDictionary<string, PredictiveModel>()
        let abTestGroups = ConcurrentDictionary<string, string>() // requestId -> algorithm variant
        let mutable lastRetrainingTime = DateTimeOffset.MinValue
        
        /// Add performance data point
        member _.RecordPerformance(providerId: AIFramework.ProviderId, model: string, actualCost: decimal, estimatedCost: decimal, 
                                  actualLatency: TimeSpan, estimatedLatency: TimeSpan, successRate: float, 
                                  ?qualityScore: float, ?tokensUsed: int, ?imagesProcessed: int, 
                                  ?requestType: string, ?userSatisfaction: float) =
            let historyPoint = {
                ProviderId = providerId
                Model = model
                TimestampUtc = DateTimeOffset.UtcNow
                ActualCost = actualCost
                EstimatedCost = estimatedCost
                ActualLatency = actualLatency
                EstimatedLatency = estimatedLatency
                QualityScore = qualityScore
                SuccessRate = successRate
                TokensUsed = tokensUsed
                ImagesProcessed = imagesProcessed
                RequestType = defaultArg requestType "text"
                UserSatisfaction = userSatisfaction
            }
            
            performanceHistory.Enqueue(historyPoint)
            
            // Trim old data if needed
            this.TrimOldData()
            
            auditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.Info,
                    AuditLogger.Info,
                    "AdvancedCostOptimizer",
                    $"Recorded performance data for {providerId} - Cost: ${actualCost:F4}, Latency: {actualLatency.TotalSeconds:F2}s",
                    Map.ofList [
                        ("providerId", box (providerId.ToString()))
                        ("model", box model)
                        ("actualCost", box actualCost)
                        ("costAccuracy", box (1.0 - abs(float(actualCost - estimatedCost)) / float estimatedCost))
                        ("successRate", box successRate)
                    ]
                )
            )
        
        /// Trim old performance data outside the window
        member _.TrimOldData() =
            let cutoffDate = DateTimeOffset.UtcNow.AddDays(-float config.HistoryWindowDays)
            let historyArray = performanceHistory.ToArray()
            
            // Clear and re-add only recent data
            while not performanceHistory.IsEmpty do
                let mutable item = Unchecked.defaultof<ProviderPerformanceHistory>
                performanceHistory.TryDequeue(&item) |> ignore
            
            for item in historyArray do
                if item.TimestampUtc >= cutoffDate then
                    performanceHistory.Enqueue(item)
        
        /// Get recent performance data for analysis
        member _.GetRecentPerformanceData(?days: int) =
            let daysBack = defaultArg days config.HistoryWindowDays
            let cutoffDate = DateTimeOffset.UtcNow.AddDays(-float daysBack)
            
            performanceHistory.ToArray()
            |> Array.filter (fun h -> h.TimestampUtc >= cutoffDate)
            |> Array.toList
        
        /// Calculate dynamic weights based on recent performance
        member this.CalculateDynamicWeights(providerId: AIFramework.ProviderId) =
            let recentData = this.GetRecentPerformanceData(7) // Last 7 days
            let providerData = recentData |> List.filter (fun h -> h.ProviderId = providerId)
            
            if providerData.Length < 5 then
                // Not enough data, use default weights
                Map.ofList [
                    ("cost", 0.3)
                    ("quality", 0.25)
                    ("latency", 0.2)
                    ("reliability", 0.15)
                    ("satisfaction", 0.1)
                ]
            else
                // Calculate performance metrics
                let avgCostAccuracy = 
                    providerData 
                    |> List.averageBy (fun h -> 1.0 - abs(float(h.ActualCost - h.EstimatedCost)) / max (float h.EstimatedCost) 0.001)
                
                let avgLatencyAccuracy = 
                    providerData 
                    |> List.averageBy (fun h -> 1.0 - abs(h.ActualLatency.TotalSeconds - h.EstimatedLatency.TotalSeconds) / max h.EstimatedLatency.TotalSeconds 0.001)
                
                let avgSuccessRate = providerData |> List.averageBy (fun h -> h.SuccessRate)
                let avgSatisfaction = 
                    providerData 
                    |> List.choose (fun h -> h.UserSatisfaction)
                    |> function | [] -> 0.8 | scores -> List.average scores
                
                // Adjust weights based on performance
                let costWeight = if avgCostAccuracy > 0.9 then 0.35 else 0.25
                let qualityWeight = 
                    match providerData |> List.choose (fun h -> h.QualityScore) with
                    | [] -> 0.25
                    | scores -> if List.average scores > 0.9 then 0.3 else 0.2
                let latencyWeight = if avgLatencyAccuracy > 0.8 then 0.15 else 0.25
                let reliabilityWeight = if avgSuccessRate > 0.95 then 0.1 else 0.2
                let satisfactionWeight = if avgSatisfaction > 0.9 then 0.15 else 0.1
                
                // Normalize weights to sum to 1.0
                let totalWeight = costWeight + qualityWeight + latencyWeight + reliabilityWeight + satisfactionWeight
                
                Map.ofList [
                    ("cost", costWeight / totalWeight)
                    ("quality", qualityWeight / totalWeight)
                    ("latency", latencyWeight / totalWeight)
                    ("reliability", reliabilityWeight / totalWeight)
                    ("satisfaction", satisfactionWeight / totalWeight)
                ]
        
        /// Train predictive model using historical data
        member this.TrainPredictiveModel(modelType: ModelType) =
            let recentData = this.GetRecentPerformanceData()
            
            if recentData.Length < config.MinHistoricalDataPoints then
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Warning,
                        AuditLogger.Warning,
                        "AdvancedCostOptimizer",
                        $"Insufficient data for training: {recentData.Length} < {config.MinHistoricalDataPoints}",
                        Map.ofList [("dataPoints", box recentData.Length)]
                    )
                )
                None
            else
                match modelType with
                | LinearRegression ->
                    this.TrainLinearRegressionModel(recentData)
                | WeightedAverage ->
                    this.TrainWeightedAverageModel(recentData)
                | TimeSeriesForecasting ->
                    this.TrainTimeSeriesModel(recentData)
                | NeuralNetwork ->
                    // Placeholder for neural network implementation
                    this.TrainLinearRegressionModel(recentData) // Fallback
                | EnsembleModel(models) ->
                    this.TrainEnsembleModel(models, recentData)
        
        /// Train linear regression model (simplified implementation)
        member _.TrainLinearRegressionModel(data: ProviderPerformanceHistory list) =
            try
                // Group by provider and calculate linear trends
                let providerGroups = data |> List.groupBy (fun h -> h.ProviderId)
                let parameters = ConcurrentDictionary<string, float>()
                
                for (providerId, providerData) in providerGroups do
                    // Calculate cost trend (simplified linear regression)
                    let costs = providerData |> List.map (fun h -> float h.ActualCost)
                    let avgCost = List.average costs
                    let costVariance = costs |> List.averageBy (fun c -> (c - avgCost) ** 2.0)
                    
                    // Calculate latency trend
                    let latencies = providerData |> List.map (fun h -> h.ActualLatency.TotalSeconds)
                    let avgLatency = List.average latencies
                    let latencyVariance = latencies |> List.averageBy (fun l -> (l - avgLatency) ** 2.0)
                    
                    // Store parameters
                    parameters.[$"{providerId}_cost_avg"] <- avgCost
                    parameters.[$"{providerId}_cost_variance"] <- costVariance
                    parameters.[$"{providerId}_latency_avg"] <- avgLatency
                    parameters.[$"{providerId}_latency_variance"] <- latencyVariance
                
                let model = {
                    ModelId = $"linear_regression_{DateTimeOffset.UtcNow:yyyyMMdd_HHmmss}"
                    ModelType = LinearRegression
                    TrainingData = data
                    LastTrainingDate = DateTimeOffset.UtcNow
                    AccuracyScore = 0.75 // Simplified accuracy calculation
                    Parameters = parameters.ToArray() |> Array.map (fun kvp -> (kvp.Key, kvp.Value)) |> Map.ofArray
                    IsActive = true
                }
                
                models.[model.ModelId] <- model
                lastRetrainingTime <- DateTimeOffset.UtcNow
                
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Info,
                        AuditLogger.Info,
                        "AdvancedCostOptimizer",
                        $"Trained linear regression model {model.ModelId} with {data.Length} data points",
                        Map.ofList [
                            ("modelId", box model.ModelId)
                            ("dataPoints", box data.Length)
                            ("accuracyScore", box model.AccuracyScore)
                        ]
                    )
                )
                
                Some model
                
            with
            | ex ->
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Error,
                        AuditLogger.Error,
                        "AdvancedCostOptimizer",
                        $"Failed to train linear regression model: {ex.Message}",
                        Map.ofList [("exception", box ex.Message)]
                    )
                )
                None
        
        /// Train weighted average model
        member _.TrainWeightedAverageModel(data: ProviderPerformanceHistory list) =
            try
                let parameters = ConcurrentDictionary<string, float>()
                let now = DateTimeOffset.UtcNow
                
                // Group by provider and calculate weighted averages
                let providerGroups = data |> List.groupBy (fun h -> h.ProviderId)
                
                for (providerId, providerData) in providerGroups do
                    // Weight recent data more heavily (exponential decay)
                    let weightedCost = 
                        providerData
                        |> List.sumBy (fun h -> 
                            let daysSince = (now - h.TimestampUtc).TotalDays
                            let weight = Math.Exp(-daysSince / 7.0) // Decay over 7 days
                            float h.ActualCost * weight)
                    
                    let totalWeight = 
                        providerData
                        |> List.sumBy (fun h -> 
                            let daysSince = (now - h.TimestampUtc).TotalDays
                            Math.Exp(-daysSince / 7.0))
                    
                    let weightedAvgCost = if totalWeight > 0.0 then weightedCost / totalWeight else 0.0
                    
                    // Similar calculation for latency
                    let weightedLatency = 
                        providerData
                        |> List.sumBy (fun h -> 
                            let daysSince = (now - h.TimestampUtc).TotalDays
                            let weight = Math.Exp(-daysSince / 7.0)
                            h.ActualLatency.TotalSeconds * weight)
                    
                    let weightedAvgLatency = if totalWeight > 0.0 then weightedLatency / totalWeight else 0.0
                    
                    parameters.[$"{providerId}_weighted_cost"] <- weightedAvgCost
                    parameters.[$"{providerId}_weighted_latency"] <- weightedAvgLatency
                    parameters.[$"{providerId}_sample_size"] <- float providerData.Length
                
                let model = {
                    ModelId = $"weighted_average_{DateTimeOffset.UtcNow:yyyyMMdd_HHmmss}"
                    ModelType = WeightedAverage
                    TrainingData = data
                    LastTrainingDate = DateTimeOffset.UtcNow
                    AccuracyScore = 0.8 // Weighted averages tend to be more accurate
                    Parameters = parameters.ToArray() |> Array.map (fun kvp -> (kvp.Key, kvp.Value)) |> Map.ofArray
                    IsActive = true
                }
                
                models.[model.ModelId] <- model
                Some model
                
            with
            | ex ->
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Error,
                        AuditLogger.Error,
                        "AdvancedCostOptimizer",
                        $"Failed to train weighted average model: {ex.Message}",
                        Map.ofList [("exception", box ex.Message)]
                    )
                )
                None
        
        /// Train time series forecasting model (simplified)
        member _.TrainTimeSeriesModel(data: ProviderPerformanceHistory list) =
            // Placeholder for more sophisticated time series analysis
            // In a real implementation, this could use ARIMA, LSTM, or other time series methods
            this.TrainWeightedAverageModel(data)
        
        /// Train ensemble model combining multiple approaches
        member this.TrainEnsembleModel(modelTypes: ModelType list, data: ProviderPerformanceHistory list) =
            let subModels = modelTypes |> List.choose (fun mt -> this.TrainPredictiveModel(mt))
            
            if subModels.IsEmpty then None
            else
                let ensembleParameters = 
                    subModels 
                    |> List.mapi (fun i model -> ($"submodel_{i}_weight", 1.0 / float subModels.Length))
                    |> Map.ofList
                
                let ensembleModel = {
                    ModelId = $"ensemble_{DateTimeOffset.UtcNow:yyyyMMdd_HHmmss}"
                    ModelType = EnsembleModel(modelTypes)
                    TrainingData = data
                    LastTrainingDate = DateTimeOffset.UtcNow
                    AccuracyScore = subModels |> List.averageBy (fun m -> m.AccuracyScore)
                    Parameters = ensembleParameters
                    IsActive = true
                }
                
                models.[ensembleModel.ModelId] <- ensembleModel
                Some ensembleModel
        
        /// Predict provider performance using trained models
        member this.PredictProviderPerformance(providerId: AIFramework.ProviderId, model: string, tokenCount: int option, imageCount: int option) =
            let activeModels = models.Values |> Seq.filter (fun m -> m.IsActive) |> Seq.toList
            
            if activeModels.IsEmpty then
                // Fallback to basic estimation
                basicOptimizer.EstimateCost(providerId, model, tokenCount, imageCount)
            else
                // Use the most recent and accurate model
                let bestModel = activeModels |> List.maxBy (fun m -> m.AccuracyScore * (1.0 + (DateTimeOffset.UtcNow - m.LastTrainingDate).TotalDays))
                
                let costKey = $"{providerId}_weighted_cost"
                let latencyKey = $"{providerId}_weighted_latency"
                
                let predictedCost = 
                    match bestModel.Parameters.TryFind(costKey) with
                    | Some cost -> decimal cost
                    | None -> 
                        // Fallback to basic estimation
                        match basicOptimizer.EstimateCost(providerId, model, tokenCount, imageCount) with
                        | Some estimate -> estimate.EstimatedCost
                        | None -> 0.01m
                
                let predictedLatency = 
                    match bestModel.Parameters.TryFind(latencyKey) with
                    | Some latency -> TimeSpan.FromSeconds(latency)
                    | None -> TimeSpan.FromSeconds(2.0)
                
                // Adjust predictions based on request characteristics
                let adjustedCost = 
                    match tokenCount with
                    | Some tokens -> predictedCost * (1.0m + decimal tokens / 10000m) // Scale with token count
                    | None -> predictedCost
                
                let finalCost = 
                    match imageCount with
                    | Some images -> adjustedCost + (decimal images * 0.01m) // Add image processing cost
                    | None -> adjustedCost
                
                Some {
                    ProviderId = providerId
                    Model = model
                    EstimatedCost = finalCost
                    TokenCount = tokenCount
                    ImageCount = imageCount
                    QualityScore = 0.85 // Could be predicted from model
                    ReliabilityScore = 0.9 // Could be predicted from model
                    ExpectedLatency = predictedLatency
                    Confidence = bestModel.AccuracyScore
                    Reasoning = [
                        sprintf "Prediction based on %A model" bestModel.ModelType
                        sprintf "Model accuracy: %.2f" bestModel.AccuracyScore
                        sprintf "Training data: %d points" bestModel.TrainingData.Length
                        sprintf "Last trained: %s" (bestModel.LastTrainingDate.ToString("yyyy-MM-dd"))
                    ]
                }
        
        /// Advanced provider selection using ML predictions and optimization goals
        member this.SelectProviderAdvanced(requestId: string, model: string, tokenCount: int option, imageCount: int option, ?goals: OptimizationGoal list) =
            let optimizationGoals = defaultArg goals config.OptimizationGoals
            
            // Check if we need to retrain models
            if config.EnableMachineLearning && 
               (DateTimeOffset.UtcNow - lastRetrainingTime).TotalHours >= float config.RetrainingIntervalHours then
                this.TrainPredictiveModel(WeightedAverage) |> ignore
            
            // Get predictions for all providers
            let availableProviders = [AIFramework.OpenAI; AIFramework.Anthropic; AIFramework.GoogleGemini]
            let predictions = 
                availableProviders 
                |> List.choose (fun providerId -> this.PredictProviderPerformance(providerId, model, tokenCount, imageCount))
                |> List.filter (fun p -> p.Confidence >= config.PredictionConfidenceThreshold)
            
            if predictions.IsEmpty then
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Warning,
                        AuditLogger.Warning,
                        "AdvancedCostOptimizer",
                        "No confident predictions available, falling back to basic selection",
                        Map.ofList [("requestId", box requestId)]
                    )
                )
                
                if config.FallbackToBasicSelection then
                    basicOptimizer.SelectProvider(BalancedCostQuality, model, tokenCount, imageCount)
                else
                    None
            else
                // Apply optimization goals
                let scoredPredictions = 
                    predictions 
                    |> List.map (fun p -> 
                        let scores = this.CalculateOptimizationScores(p, optimizationGoals)
                        (p, scores))
                
                let bestPrediction = scoredPredictions |> List.maxBy (fun (_, scores) -> scores.TotalScore)
                
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Info,
                        AuditLogger.Info,
                        "AdvancedCostOptimizer",
                        $"Advanced selection: {(fst bestPrediction).ProviderId} with score {(snd bestPrediction).TotalScore:F3}",
                        Map.ofList [
                            ("requestId", box requestId)
                            ("providerId", box ((fst bestPrediction).ProviderId.ToString()))
                            ("confidence", box (fst bestPrediction).Confidence)
                            ("totalScore", box (snd bestPrediction).TotalScore)
                        ]
                    )
                )
                
                Some (fst bestPrediction)
        
        /// Calculate optimization scores for a prediction
        member _.CalculateOptimizationScores(prediction: CostEstimate, goals: OptimizationGoal list) =
            let scores = ResizeArray<float>()
            let weights = ResizeArray<float>()
            
            for goal in goals do
                match goal with
                | MinimizeCost -> 
                    let costScore = 1.0 - (float prediction.EstimatedCost / 1.0) // Normalize to max $1
                    scores.Add(Math.Max(0.0, costScore))
                    weights.Add(1.0)
                
                | MaximizeQuality -> 
                    scores.Add(prediction.QualityScore)
                    weights.Add(1.0)
                
                | MinimizeLatency -> 
                    let latencyScore = 1.0 - (prediction.ExpectedLatency.TotalSeconds / 30.0) // Normalize to max 30s
                    scores.Add(Math.Max(0.0, latencyScore))
                    weights.Add(1.0)
                
                | MaximizeReliability -> 
                    scores.Add(prediction.ReliabilityScore)
                    weights.Add(1.0)
                
                | MaximizeUserSatisfaction -> 
                    scores.Add(0.8) // Default if no satisfaction data
                    weights.Add(0.5) // Lower weight since it's estimated
                
                | BalanceMultipleGoals(goalWeights) ->
                    let subScores = ResizeArray<float>()
                    for (subGoal, weight) in goalWeights |> Map.toList do
                        let subScore = this.CalculateOptimizationScores(prediction, [subGoal])
                        subScores.Add(subScore.TotalScore * weight)
                    scores.Add(subScores |> Seq.sum)
                    weights.Add(1.0)
            
            let totalScore = 
                if weights.Count = 0 then 0.0
                else (Seq.zip scores weights |> Seq.sumBy (fun (s, w) -> s * w)) / (weights |> Seq.sum)
            
            {| TotalScore = totalScore; ComponentScores = scores.ToArray(); Weights = weights.ToArray() |}
        
        /// Get performance analytics and insights
        member this.GetPerformanceAnalytics() =
            let recentData = this.GetRecentPerformanceData()
            let providerGroups = recentData |> List.groupBy (fun h -> h.ProviderId)
            
            let providerAnalytics = 
                providerGroups
                |> List.map (fun (providerId, data) ->
                    let costAccuracy = 
                        data 
                        |> List.averageBy (fun h -> 1.0 - abs(float(h.ActualCost - h.EstimatedCost)) / max (float h.EstimatedCost) 0.001)
                    
                    let latencyAccuracy = 
                        data 
                        |> List.averageBy (fun h -> 1.0 - abs(h.ActualLatency.TotalSeconds - h.EstimatedLatency.TotalSeconds) / max h.EstimatedLatency.TotalSeconds 0.001)
                    
                    let avgSuccessRate = data |> List.averageBy (fun h -> h.SuccessRate)
                    let avgCost = data |> List.averageBy (fun h -> float h.ActualCost)
                    let avgLatency = data |> List.averageBy (fun h -> h.ActualLatency.TotalSeconds)
                    
                    {|
                        ProviderId = providerId
                        RequestCount = data.Length
                        CostAccuracy = costAccuracy
                        LatencyAccuracy = latencyAccuracy
                        SuccessRate = avgSuccessRate
                        AverageCost = avgCost
                        AverageLatency = avgLatency
                        Recommendation = 
                            if costAccuracy > 0.9 && avgSuccessRate > 0.95 then "Excellent"
                            elif costAccuracy > 0.8 && avgSuccessRate > 0.9 then "Good"
                            elif costAccuracy > 0.6 && avgSuccessRate > 0.8 then "Acceptable"
                            else "Needs Improvement"
                    |})
                |> List.sortByDescending (fun p -> p.SuccessRate * p.CostAccuracy)
            
            let modelAnalytics = 
                models.Values 
                |> Seq.map (fun model -> {|
                    ModelId = model.ModelId
                    ModelType = model.ModelType.ToString()
                    AccuracyScore = model.AccuracyScore
                    LastTrainingDate = model.LastTrainingDate
                    DataPoints = model.TrainingData.Length
                    IsActive = model.IsActive
                |})
                |> Seq.toList
            
            {|
                ProviderAnalytics = providerAnalytics
                ModelAnalytics = modelAnalytics
                TotalDataPoints = recentData.Length
                AnalysisPeriodDays = config.HistoryWindowDays
                LastAnalysisTime = DateTimeOffset.UtcNow
            |}
    
    /// Create advanced cost optimizer
    let createAdvancedCostOptimizer (config: AdvancedAlgorithmConfig option) (basicOptimizer: CostOptimizer) (auditLogger: AuditLogger.AuditLogger option) =
        let config = defaultArg config defaultAdvancedConfig
        new AdvancedCostOptimizer(config, basicOptimizer, auditLogger)
