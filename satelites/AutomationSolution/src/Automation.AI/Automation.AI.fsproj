﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net10.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="Library.fs" />
    <Compile Include="SecurityValidator.fs" />
    <Compile Include="AuditLogger.fs" />
    <Compile Include="RateLimiter.fs" />
    <Compile Include="SecureCredentialManager.fs" />
    <Compile Include="ErrorHandling.fs" />
    <Compile Include="HealthChecks.fs" />
    <Compile Include="ResourcePoolManager.fs" />
    <Compile Include="CacheOptimizer.fs" />
    <Compile Include="AutoHealAgent.fs" />
    <Compile Include="PerformanceMonitor.fs" />
    <Compile Include="CostOptimizer.fs" />
    <Compile Include="AdvancedCostAlgorithms.fs" />
    <Compile Include="IntegratedCostAwareSystem.fs" />
    <Compile Include="CostAwareSelectionTests.fs" />
    <Compile Include="MemoryCpuOptimizer.fs" />
    <Compile Include="LoadBalancer.fs" />
    <Compile Include="WorkerCoordination.fs" />
    <Compile Include="PerformanceTesting.fs" />
    <Compile Include="PerformanceReporting.fs" />
    <Compile Include="PerformanceTestExecutor.fs" />
    <Compile Include="PerformanceTestDemo.fs" />
    <!-- <Compile Include="SystemTuning.fs" /> -->
    <!-- <Compile Include="SystemTuningOrchestrator.fs" /> -->
    <Compile Include="SimplifiedSystemTuning.fs" />
    <Compile Include="ObservabilityEndpoints.fs" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Automation.Core\Automation.Core.fsproj" />
    <ProjectReference Include="..\Automation.Data\Automation.Data.fsproj" />
    <ProjectReference Include="..\Automation.Web\Automation.Web.fsproj" />
    <ProjectReference Include="..\Automation.Prompts\Automation.Prompts.fsproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FSharp.Data" Version="6.6.0" />
    <PackageReference Include="LangChain" Version="0.17.0">
      <ExcludeAssets>runtime</ExcludeAssets>
    </PackageReference>
    <PackageReference Include="System.Text.Json" Version="9.0.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.16" />
    <PackageReference Include="FSharp.SystemTextJson" Version="1.3.13" />
    <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

</Project>
