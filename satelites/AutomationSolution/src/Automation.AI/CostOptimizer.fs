namespace Automation.AI

open System
open System.Collections.Concurrent
open System.Collections.Generic
open Automation.Core

/// Cost optimization and intelligent provider routing for AI operations
module CostOptimizer =
    
    /// Cost structure for different AI providers
    type ProviderCostStructure = {
        ProviderId: AIFramework.ProviderId
        TextTokenCostPer1K: decimal // Cost per 1000 tokens for text
        ImageAnalysisCost: decimal // Cost per image
        VisionTokenCostPer1K: decimal // Cost per 1000 tokens for vision
        MinimumCharge: decimal // Minimum charge per request
        FreeRequestsPerMonth: int option
        RateLimitRpm: int // Requests per minute
        RateLimitTpm: int // Tokens per minute
        QualityScore: float // Quality rating 0.0-1.0
        ReliabilityScore: float // Reliability rating 0.0-1.0
        LatencyScore: float // Speed rating 0.0-1.0 (higher is faster)
        SupportedModels: string list
        TierPricing: Map<int, decimal> option // Volume discounts
        Currency: string
        LastUpdated: DateTimeOffset
    }
    
    /// Request cost estimation
    type CostEstimate = {
        ProviderId: AIFramework.ProviderId
        Model: string
        EstimatedCost: decimal
        TokenCount: int option
        ImageCount: int option
        QualityScore: float
        ReliabilityScore: float
        ExpectedLatency: TimeSpan
        Confidence: float // How confident we are in the estimate
        Reasoning: string list // Why this provider was selected
    }
    
    /// Provider selection strategy
    type SelectionStrategy =
        | CostMinimization // Cheapest option
        | QualityMaximization // Best quality regardless of cost
        | BalancedCostQuality // Balance between cost and quality
        | LatencyOptimized // Fastest response time
        | ReliabilityFocused // Most reliable provider
        | Custom of (CostEstimate list -> CostEstimate option) // Custom selection logic
    
    /// Cost optimization configuration
    type CostOptimizerConfig = {
        DefaultStrategy: SelectionStrategy
        MaxCostPerRequest: decimal option
        MaxAcceptableLatency: TimeSpan option
        MinQualityScore: float option
        MinReliabilityScore: float option
        EnableProviderFallback: bool
        FallbackProviders: AIFramework.ProviderId list
        TrackingEnabled: bool
        CostAlertThreshold: decimal option // Alert if daily costs exceed this
        MonthlyBudget: decimal option
        ProviderWeights: Map<string, float> // Custom weights for selection criteria
    }
    
    /// Default cost optimizer configuration
    let defaultCostOptimizerConfig = {
        DefaultStrategy = BalancedCostQuality
        MaxCostPerRequest = Some 1.0m
        MaxAcceptableLatency = Some (TimeSpan.FromSeconds(30.0))
        MinQualityScore = Some 0.7
        MinReliabilityScore = Some 0.8
        EnableProviderFallback = true
        FallbackProviders = [AIFramework.OpenAI; AIFramework.Anthropic]
        TrackingEnabled = true
        CostAlertThreshold = Some 100.0m
        MonthlyBudget = Some 1000.0m
        ProviderWeights = Map.ofList [
            ("cost", 0.4)
            ("quality", 0.3)
            ("reliability", 0.2)
            ("latency", 0.1)
        ]
    }
    
    /// Sample provider cost structures (would be loaded from configuration or API)
    let sampleProviderCosts = [
        {
            ProviderId = AIFramework.OpenAI
            TextTokenCostPer1K = 0.002m
            ImageAnalysisCost = 0.01m
            VisionTokenCostPer1K = 0.01m
            MinimumCharge = 0.0001m
            FreeRequestsPerMonth = None
            RateLimitRpm = 3500
            RateLimitTpm = 90000
            QualityScore = 0.95
            ReliabilityScore = 0.95
            LatencyScore = 0.8
            SupportedModels = ["gpt-4"; "gpt-3.5-turbo"; "gpt-4-vision-preview"]
            TierPricing = None
            Currency = "USD"
            LastUpdated = DateTimeOffset.Now
        }
        {
            ProviderId = AIFramework.Anthropic
            TextTokenCostPer1K = 0.008m
            ImageAnalysisCost = 0.012m
            VisionTokenCostPer1K = 0.012m
            MinimumCharge = 0.0001m
            FreeRequestsPerMonth = None
            RateLimitRpm = 1000
            RateLimitTpm = 40000
            QualityScore = 0.93
            ReliabilityScore = 0.92
            LatencyScore = 0.75
            SupportedModels = ["claude-3-sonnet"; "claude-3-haiku"; "claude-3-opus"]
            TierPricing = None
            Currency = "USD"
            LastUpdated = DateTimeOffset.Now
        }
        {
            ProviderId = AIFramework.GoogleGemini
            TextTokenCostPer1K = 0.0005m
            ImageAnalysisCost = 0.005m
            VisionTokenCostPer1K = 0.005m
            MinimumCharge = 0.0001m
            FreeRequestsPerMonth = Some 1000
            RateLimitRpm = 2000
            RateLimitTpm = 50000
            QualityScore = 0.85
            ReliabilityScore = 0.88
            LatencyScore = 0.9
            SupportedModels = ["gemini-pro"; "gemini-pro-vision"]
            TierPricing = None
            Currency = "USD"
            LastUpdated = DateTimeOffset.Now
        }
    ]
    
    /// Cost tracking record
    type CostTracking = {
        Date: DateOnly
        ProviderId: AIFramework.ProviderId
        Model: string
        RequestCount: int
        TokensUsed: int
        ImagesProcessed: int
        TotalCost: decimal
        AverageCostPerRequest: decimal
    }
    
    /// Cost optimizer main class
    type CostOptimizer(config: CostOptimizerConfig, auditLogger: AuditLogger.AuditLogger option) =
        let providerCosts = ConcurrentDictionary<AIFramework.ProviderId, ProviderCostStructure>()
        let dailyCosts = ConcurrentDictionary<DateOnly, decimal>()
        let monthlyCosts = ConcurrentDictionary<(int * int), decimal>() // (year, month) -> cost
        let costTracking = ConcurrentQueue<CostTracking>()
        
        do
            // Initialize with sample costs (in production, load from config/API)
            for cost in sampleProviderCosts do
                providerCosts.[cost.ProviderId] <- cost
        
        /// Update provider cost structure
        member _.UpdateProviderCosts(costs: ProviderCostStructure) =
            providerCosts.[costs.ProviderId] <- costs
            
            auditLogger |> Option.iter (fun logger ->
                logger.LogEvent(
                    AuditLogger.Info,
                    AuditLogger.Info,
                    "CostOptimizer",
                    $"Updated cost structure for provider {costs.ProviderId}",
                    Map.ofList [
                        ("providerId", box (costs.ProviderId.ToString()))
                        ("textCostPer1K", box costs.TextTokenCostPer1K)
                        ("qualityScore", box costs.QualityScore)
                    ]
                )
            )
        
        /// Estimate cost for a request
        member _.EstimateCost(providerId: AIFramework.ProviderId, model: string, tokenCount: int option, imageCount: int option) =
            match providerCosts.TryGetValue(providerId) with
            | true, costs ->
                let textCost = 
                    match tokenCount with
                    | Some tokens -> (decimal tokens / 1000m) * costs.TextTokenCostPer1K
                    | None -> 0m
                
                let imageCost = 
                    match imageCount with
                    | Some images -> decimal images * costs.ImageAnalysisCost
                    | None -> 0m
                
                let totalCost = max (textCost + imageCost) costs.MinimumCharge
                
                let expectedLatency = 
                    // Estimate latency based on provider characteristics and load
                    let baseLatency = 1.0 / costs.LatencyScore * 2.0 // 2 seconds base for score 1.0
                    let tokenLatency = 
                        match tokenCount with
                        | Some tokens -> float tokens / 1000.0 * 0.5 // 0.5s per 1k tokens
                        | None -> 0.0
                    TimeSpan.FromSeconds(baseLatency + tokenLatency)
                
                let reasoning = [
                    $"Base cost: ${textCost:F4} for text + ${imageCost:F4} for images"
                    $"Quality score: {costs.QualityScore:F2}"
                    $"Reliability score: {costs.ReliabilityScore:F2}"
                    $"Expected latency: {expectedLatency.TotalSeconds:F1}s"
                ]
                
                Some {
                    ProviderId = providerId
                    Model = model
                    EstimatedCost = totalCost
                    TokenCount = tokenCount
                    ImageCount = imageCount
                    QualityScore = costs.QualityScore
                    ReliabilityScore = costs.ReliabilityScore
                    ExpectedLatency = expectedLatency
                    Confidence = 0.85 // Fixed confidence, could be dynamic
                    Reasoning = reasoning
                }
            | false, _ -> None
        
        /// Get cost estimates for all available providers
        member this.GetAllCostEstimates(model: string, tokenCount: int option, imageCount: int option) =
            providerCosts.Values
            |> Seq.choose (fun cost -> this.EstimateCost(cost.ProviderId, model, tokenCount, imageCount))
            |> List.ofSeq
        
        /// Select optimal provider based on strategy
        member this.SelectProvider(strategy: SelectionStrategy, model: string, tokenCount: int option, imageCount: int option) =
            let estimates = this.GetAllCostEstimates(model, tokenCount, imageCount)
            
            // Filter by configuration constraints
            let filteredEstimates = 
                estimates
                |> List.filter (fun est ->
                    let meetsCostLimit = 
                        match config.MaxCostPerRequest with
                        | Some maxCost -> est.EstimatedCost <= maxCost
                        | None -> true
                    
                    let meetsLatencyLimit = 
                        match config.MaxAcceptableLatency with
                        | Some maxLatency -> est.ExpectedLatency <= maxLatency
                        | None -> true
                    
                    let meetsQualityMin = 
                        match config.MinQualityScore with
                        | Some minQuality -> est.QualityScore >= minQuality
                        | None -> true
                    
                    let meetsReliabilityMin = 
                        match config.MinReliabilityScore with
                        | Some minReliability -> est.ReliabilityScore >= minReliability
                        | None -> true
                    
                    meetsCostLimit && meetsLatencyLimit && meetsQualityMin && meetsReliabilityMin
                )
            
            if filteredEstimates.IsEmpty then
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Warning,
                        AuditLogger.Warning,
                        "CostOptimizer",
                        "No providers meet the configured constraints",
                        Map.ofList [
                            ("model", box model)
                            ("estimatesCount", box estimates.Length)
                            ("filteredCount", box filteredEstimates.Length)
                        ]
                    )
                )
                None
            else
                let selectedEstimate = 
                    match strategy with
                    | CostMinimization ->
                        filteredEstimates |> List.minBy (fun est -> est.EstimatedCost)
                    | QualityMaximization ->
                        filteredEstimates |> List.maxBy (fun est -> est.QualityScore)
                    | BalancedCostQuality ->
                        filteredEstimates 
                        |> List.maxBy (fun est -> 
                            let costScore = 1.0 - (float est.EstimatedCost / (filteredEstimates |> List.map (fun e -> float e.EstimatedCost) |> List.max))
                            let qualityScore = est.QualityScore
                            (costScore * 0.6) + (qualityScore * 0.4))
                    | LatencyOptimized ->
                        filteredEstimates |> List.minBy (fun est -> est.ExpectedLatency)
                    | ReliabilityFocused ->
                        filteredEstimates |> List.maxBy (fun est -> est.ReliabilityScore)
                    | Custom(selector) ->
                        match selector(filteredEstimates) with
                        | Some estimate -> estimate
                        | None -> filteredEstimates |> List.head // Fallback to first available
                
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Info,
                        AuditLogger.Info,
                        "CostOptimizer",
                        $"Selected provider {selectedEstimate.ProviderId} for cost ${selectedEstimate.EstimatedCost:F4}",
                        Map.ofList [
                            ("providerId", box (selectedEstimate.ProviderId.ToString()))
                            ("model", box model)
                            ("estimatedCost", box selectedEstimate.EstimatedCost)
                            ("strategy", box (strategy.ToString()))
                            ("qualityScore", box selectedEstimate.QualityScore)
                        ]
                    )
                )
                
                Some selectedEstimate
        
        /// Record actual cost after operation completion
        member _.RecordActualCost(providerId: AIFramework.ProviderId, model: string, actualCost: decimal, tokensUsed: int option, imagesProcessed: int option) =
            if config.TrackingEnabled then
                let today = DateOnly.FromDateTime(DateTime.Today)
                
                // Update daily cost
                dailyCosts.AddOrUpdate(today, actualCost, fun _ existing -> existing + actualCost) |> ignore
                
                // Update monthly cost
                let monthKey = (today.Year, today.Month)
                monthlyCosts.AddOrUpdate(monthKey, actualCost, fun _ existing -> existing + actualCost) |> ignore
                
                // Add to tracking queue
                let tracking = {
                    Date = today
                    ProviderId = providerId
                    Model = model
                    RequestCount = 1
                    TokensUsed = defaultArg tokensUsed 0
                    ImagesProcessed = defaultArg imagesProcessed 0
                    TotalCost = actualCost
                    AverageCostPerRequest = actualCost
                }
                costTracking.Enqueue(tracking)
                
                // Check for cost alerts
                this.CheckCostAlerts(today)
                
                auditLogger |> Option.iter (fun logger ->
                    logger.LogEvent(
                        AuditLogger.Info,
                        AuditLogger.Info,
                        "CostOptimizer",
                        $"Recorded cost ${actualCost:F4} for {providerId} using {model}",
                        Map.ofList [
                            ("providerId", box (providerId.ToString()))
                            ("model", box model)
                            ("actualCost", box actualCost)
                            ("tokensUsed", box tokensUsed)
                            ("imagesProcessed", box imagesProcessed)
                        ]
                    )
                )
        
        /// Check for cost alerts
        member _.CheckCostAlerts(date: DateOnly) =
            match config.CostAlertThreshold with
            | Some threshold ->
                match dailyCosts.TryGetValue(date) with
                | true, dailyCost when dailyCost > threshold ->
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Warning,
                            AuditLogger.Warning,
                            "CostOptimizer",
                            $"Daily cost threshold exceeded: ${dailyCost:F2} > ${threshold:F2}",
                            Map.ofList [
                                ("date", box date)
                                ("dailyCost", box dailyCost)
                                ("threshold", box threshold)
                            ]
                        )
                    )
                | _ -> ()
            | None -> ()
        
        /// Get daily cost
        member _.GetDailyCost(date: DateOnly) =
            match dailyCosts.TryGetValue(date) with
            | true, cost -> cost
            | false, _ -> 0m
        
        /// Get monthly cost
        member _.GetMonthlyCost(year: int, month: int) =
            match monthlyCosts.TryGetValue((year, month)) with
            | true, cost -> cost
            | false, _ -> 0m
        
        /// Get cost statistics
        member this.GetCostStatistics(?days: int) =
            let daysToAnalyze = defaultArg days 30
            let endDate = DateOnly.FromDateTime(DateTime.Today)
            let startDate = endDate.AddDays(-daysToAnalyze)
            
            let periodCosts = 
                [for i in 0 .. daysToAnalyze - 1 do
                    let date = startDate.AddDays(i)
                    this.GetDailyCost(date)]
            
            let totalCost = periodCosts |> List.sum
            let avgDailyCost = if periodCosts.IsEmpty then 0m else totalCost / decimal periodCosts.Length
            let maxDailyCost = if periodCosts.IsEmpty then 0m else periodCosts |> List.max
            let minDailyCost = if periodCosts.IsEmpty then 0m else periodCosts |> List.min
            
            {|
                PeriodDays = daysToAnalyze
                TotalCost = totalCost
                AverageDailyCost = avgDailyCost
                MaxDailyCost = maxDailyCost
                MinDailyCost = minDailyCost
                CurrentMonthCost = this.GetMonthlyCost(DateTime.Today.Year, DateTime.Today.Month)
                BudgetUtilization = 
                    match config.MonthlyBudget with
                    | Some budget -> (this.GetMonthlyCost(DateTime.Today.Year, DateTime.Today.Month) / budget) * 100m
                    | None -> 0m
                IsOverBudget = 
                    match config.MonthlyBudget with
                    | Some budget -> this.GetMonthlyCost(DateTime.Today.Year, DateTime.Today.Month) > budget
                    | None -> false
            |}
        
        /// Get provider performance comparison
        member _.GetProviderComparison() =
            let recentTracking = costTracking.ToArray() |> Array.take (min 1000 costTracking.Count)
            
            recentTracking
            |> Array.groupBy (fun t -> t.ProviderId)
            |> Array.map (fun (providerId, records) ->
                let totalCost = records |> Array.sumBy (fun r -> r.TotalCost)
                let totalRequests = records |> Array.sumBy (fun r -> r.RequestCount)
                let avgCostPerRequest = if totalRequests = 0 then 0m else totalCost / decimal totalRequests
                
                {|
                    ProviderId = providerId
                    TotalRequests = totalRequests
                    TotalCost = totalCost
                    AverageCostPerRequest = avgCostPerRequest
                    CostShare = if recentTracking.Length = 0 then 0.0 else (float totalRequests / float recentTracking.Length) * 100.0
                |})
            |> Array.sortByDescending (fun p -> p.TotalRequests)
    
    /// Create default cost optimizer
    let createCostOptimizer (config: CostOptimizerConfig option) (auditLogger: AuditLogger.AuditLogger option) =
        let config = defaultArg config defaultCostOptimizerConfig
        new CostOptimizer(config, auditLogger)