namespace Automation.AI.Tests

open System
open Automation.Core
open Automation.AI.CostOptimizer
open Automation.AI.AdvancedCostAlgorithms
open Automation.AI.IntegratedCostAwareSystem
open Automation.Utilities.Logging

/// Test and example usage of the cost-aware provider selection system
module CostAwareSelectionTests =
    
    /// Test basic cost optimizer functionality
    let testBasicCostOptimizer() =
        printfn "=== Testing Basic Cost Optimizer ==="
        
        let auditLogger = Some (AuditLogger.createConsoleLogger AuditLogger.Info)
        let optimizer = createCostOptimizer None auditLogger
        
        // Test cost estimation
        let estimate = optimizer.EstimateCost(AIFramework.OpenAI, "gpt-4", Some 1000, None)
        match estimate with
        | Some est ->
            printfn "OpenAI GPT-4 estimate: $%.4f for 1000 tokens" (float est.EstimatedCost)
            printfn $"Quality Score: {est.QualityScore:F2}"
            printfn $"Expected Latency: {est.ExpectedLatency.TotalSeconds:F1}s"
        | None ->
            printfn "Failed to get estimate for OpenAI"
        
        // Test provider selection
        let selection = optimizer.SelectProvider(BalancedCostQuality, "gpt-4", Some 500, Some 2)
        match selection with
        | Some selected ->
            printfn $"Selected provider: {selected.ProviderId}"
            printfn $"Estimated cost: ${selected.EstimatedCost:F4}"
            printfn $"""Selection reasoning: {String.concat "; " selected.Reasoning}"""
        | None ->
            printfn "No provider selected"
        
        // Record some performance data
        optimizer.RecordActualCost(AIFramework.OpenAI, "gpt-4", 0.045m, Some 500, Some 2)
        optimizer.RecordActualCost(AIFramework.GoogleGemini, "gemini-pro", 0.012m, Some 600, None)
        
        // Get statistics
        let stats = optimizer.GetCostStatistics(7)
        printfn $"7-day cost statistics:"
        printfn $"  Total cost: ${stats.TotalCost:F2}"
        printfn $"  Average daily cost: ${stats.AverageDailyCost:F2}"
        printfn $"  Current month cost: ${stats.CurrentMonthCost:F2}"
        
        printfn ""
    
    /// Test advanced cost algorithms
    let testAdvancedCostAlgorithms() =
        printfn "=== Testing Advanced Cost Algorithms ==="
        
        let auditLogger = Some (AuditLogger.createConsoleLogger AuditLogger.Info)
        let basicOptimizer = createCostOptimizer None auditLogger
        let advancedOptimizer = createAdvancedCostOptimizer None basicOptimizer auditLogger
        
        // Simulate historical performance data
        let simulatePerformanceData() =
            let random = Random()
            for i in 1..100 do
                let providerId = 
                    match i % 3 with
                    | 0 -> AIFramework.OpenAI
                    | 1 -> AIFramework.Anthropic
                    | _ -> AIFramework.GoogleGemini
                
                let actualCost = decimal (random.NextDouble() * 0.1 + 0.01)
                let estimatedCost = actualCost * decimal (random.NextDouble() * 0.4 + 0.8) // ±20% variance
                let actualLatency = TimeSpan.FromSeconds(random.NextDouble() * 5.0 + 1.0)
                let estimatedLatency = TimeSpan.FromSeconds(actualLatency.TotalSeconds * (random.NextDouble() * 0.6 + 0.7))
                let successRate = random.NextDouble() * 0.3 + 0.7 // 70-100% success rate
                let qualityScore = random.NextDouble() * 0.3 + 0.7 // 70-100% quality
                
                advancedOptimizer.RecordPerformance(
                    providerId, "test-model", actualCost, estimatedCost, actualLatency, estimatedLatency, successRate,
                    qualityScore, Some (random.Next(100, 2000)), None, "text", Some qualityScore)
        
        printfn "Simulating 100 performance data points..."
        simulatePerformanceData()
        
        // Train predictive models
        printfn "Training predictive models..."
        let linearModel = advancedOptimizer.TrainPredictiveModel(LinearRegression)
        let weightedModel = advancedOptimizer.TrainPredictiveModel(WeightedAverage)
        
        match linearModel with
        | Some model -> printfn "Linear model trained with accuracy: %.2f" model.AccuracyScore
        | None -> printfn "Failed to train linear model"
        
        match weightedModel with
        | Some model -> printfn $"Weighted average model trained with accuracy: {model.AccuracyScore:F2}"
        | None -> printfn "Failed to train weighted model"
        
        // Test advanced selection
        printfn "Testing advanced provider selection..."
        let prediction = advancedOptimizer.SelectProviderAdvanced("test-request-1", "gpt-4", Some 1000, None, [MinimizeCost; MaximizeQuality])
        match prediction with
        | Some pred ->
            printfn $"Advanced selection: {pred.ProviderId}"
            printfn $"Estimated cost: ${pred.EstimatedCost:F4}"
            printfn $"Confidence: {pred.Confidence:F2}"
            printfn $"""Reasoning: {String.concat "; " pred.Reasoning}"""
        | None ->
            printfn "Advanced selection failed"
        
        // Get performance analytics
        let analytics = advancedOptimizer.GetPerformanceAnalytics()
        printfn "Performance analytics:"
        printfn "  Total data points: %d" analytics.TotalDataPoints
        printfn "  Provider analytics:"
        for provider in analytics.ProviderAnalytics do
            printfn "    %A: %d requests, %s rating" provider.ProviderId provider.RequestCount provider.Recommendation
        
        printfn ""
    
    /// Test integrated cost-aware system
    let testIntegratedSystem() =
        printfn "=== Testing Integrated Cost-Aware System ==="
        
        let auditLogger = Some (AuditLogger.createConsoleLogger AuditLogger.Info)
        let config = {
            defaultIntegratedConfig with
                ABTestingEnabled = true
                ABTestPercentage = 0.5
        }
        let system = createIntegratedSystem (Some config) auditLogger
        
        // Test different request contexts
        let testRequests = [
            {
                RequestId = "req-001"
                UserId = Some "user-123"
                RequestType = "simple_command"
                Priority = Low
                BudgetConstraint = Some 0.10m
                LatencyRequirement = Some (TimeSpan.FromSeconds(5.0))
                QualityRequirement = Some 0.8
                PreferredProviders = None
                Model = "gpt-3.5-turbo"
                TokenCount = Some 500
                ImageCount = None
                Metadata = Map.empty
            }
            {
                RequestId = "req-002"
                UserId = Some "user-456"
                RequestType = "complex_workflow"
                Priority = High
                BudgetConstraint = Some 1.0m
                LatencyRequirement = Some (TimeSpan.FromSeconds(10.0))
                QualityRequirement = Some 0.95
                PreferredProviders = Some [AIFramework.OpenAI; AIFramework.Anthropic]
                Model = "gpt-4"
                TokenCount = Some 2000
                ImageCount = Some 3
                Metadata = Map.ofList [("workflow_type", "automation"); ("complexity", "high")]
            }
            {
                RequestId = "req-003"
                UserId = Some "user-789"
                RequestType = "batch_processing"
                Priority = Critical
                BudgetConstraint = None
                LatencyRequirement = Some (TimeSpan.FromSeconds(2.0))
                QualityRequirement = Some 0.9
                PreferredProviders = None
                Model = "claude-3-sonnet"
                TokenCount = Some 1500
                ImageCount = None
                Metadata = Map.ofList [("batch_size", "100"); ("priority", "urgent")]
            }
        ]
        
        // Process each request
        for request in testRequests do
            printfn $"Processing request {request.RequestId}..."
            let result = system.SelectProvider(request) |> Async.RunSynchronously
            
            match result with
            | Ok selection ->
                printfn $"  Selected: {selection.SelectedProvider}"
                printfn $"  Cost: ${selection.EstimatedCost:F4}"
                printfn $"  Latency: {selection.EstimatedLatency.TotalSeconds:F1}s"
                printfn $"  Confidence: {selection.ConfidenceScore:F2}"
                printfn $"  Advanced algorithm: {selection.UsedAdvancedAlgorithm}"
                printfn $"""  A/B test group: {selection.ABTestGroup |> Option.defaultValue "none"}"""
                printfn $"  Budget impact: ${selection.BudgetImpact.ProjectedDailyCost:F2} daily, ${selection.BudgetImpact.ProjectedMonthlyCost:F2} monthly"
                
                // Simulate actual performance
                let actualCost = selection.EstimatedCost * decimal (0.8 + Random().NextDouble() * 0.4) // ±20% variance
                let actualLatency = TimeSpan.FromSeconds(selection.EstimatedLatency.TotalSeconds * (0.7 + Random().NextDouble() * 0.6))
                let success = Random().NextDouble() > 0.1 // 90% success rate
                let qualityScore = request.QualityRequirement |> Option.defaultValue 0.85
                
                system.RecordPerformance(
                    request.RequestId, selection.SelectedProvider, selection.Model,
                    actualCost, actualLatency, success, qualityScore, Some 0.9,
                    request.TokenCount, request.ImageCount)
                
                printfn $"  Actual performance: ${actualCost:F4}, {actualLatency.TotalSeconds:F1}s, success: {success}"
                
            | Error error ->
                printfn $"  Error: {error}"
            
            printfn ""
        
        // Get system analytics
        printfn "System Analytics:"
        let analytics = system.GetSystemAnalytics()
        printfn $"  Basic statistics - Total cost: ${analytics.BasicStatistics.TotalCost:F2}"
        printfn $"  Advanced features enabled: {analytics.SystemConfig.EnableAdvancedFeatures}"
        printfn $"  A/B testing enabled: {analytics.SystemConfig.ABTestingEnabled}"
        
        match analytics.ABTestAnalytics with
        | Some abTests ->
            printfn $"  A/B test results:"
            for group in abTests do
                printfn $"    {group.Group}: {group.RequestCount} requests"
        | None ->
            printfn $"  A/B testing not active"
        
        // Cleanup
        (system :> IDisposable).Dispose()
        printfn ""
    
    /// Run all tests
    let runAllTests() =
        printfn "Starting Cost-Aware Provider Selection Tests"
        printfn "=============================================="
        printfn ""
        
        try
            testBasicCostOptimizer()
            testAdvancedCostAlgorithms()
            testIntegratedSystem()
            
            printfn "All tests completed successfully!"
            
        with
        | ex ->
            printfn $"Test failed with exception: {ex.Message}"
            printfn $"Stack trace: {ex.StackTrace}"
    
    /// Example usage in production
    let exampleProductionUsage() =
        printfn "=== Example Production Usage ==="
        
        // Initialize system
        let auditLogger = Some (AuditLogger.createFileLogger AuditLogger.Info "cost_optimization.log")
        let config = {
            defaultIntegratedConfig with
                EnableAdvancedFeatures = true
                PerformanceTrackingEnabled = true
                ABTestingEnabled = true
                ABTestPercentage = 0.1 // 10% of requests for A/B testing
                CostBudgetLimits = Some {
                    DailyLimit = 200.0m
                    MonthlyLimit = 5000.0m
                    PerRequestLimit = 10.0m
                    EnableAutomaticThrottling = true
                }
        }
        
        use system = createIntegratedSystem (Some config) auditLogger
        
        // Example: AI automation request
        let automationRequest = {
            RequestId = Guid.NewGuid().ToString()
            UserId = Some "automation-system"
            RequestType = "web_automation"
            Priority = Medium
            BudgetConstraint = Some 0.50m
            LatencyRequirement = Some (TimeSpan.FromSeconds(8.0))
            QualityRequirement = Some 0.85
            PreferredProviders = None
            Model = "gpt-4"
            TokenCount = Some 1200
            ImageCount = Some 1 // Screenshot analysis
            Metadata = Map.ofList [
                ("automation_type", "web_navigation")
                ("target_site", "example.com")
                ("complexity", "medium")
            ]
        }
        
        // Select provider
        let selectionResult = system.SelectProvider(automationRequest) |> Async.RunSynchronously
        
        match selectionResult with
        | Ok selection ->
            printfn $"Production example - Selected provider: {selection.SelectedProvider}"
            printfn $"Estimated cost: ${selection.EstimatedCost:F4}"
            printfn $"Expected latency: {selection.EstimatedLatency.TotalSeconds:F1}s"
            printfn $"Selection confidence: {selection.ConfidenceScore:F2}"
            
            // Simulate successful execution
            let actualCost = 0.42m
            let actualLatency = TimeSpan.FromSeconds(6.5)
            let success = true
            let qualityScore = 0.88
            let userSatisfaction = 0.92
            
            system.RecordPerformance(
                automationRequest.RequestId, selection.SelectedProvider, selection.Model,
                actualCost, actualLatency, success, qualityScore, userSatisfaction,
                automationRequest.TokenCount, automationRequest.ImageCount)
            
            printfn $"Recorded actual performance: ${actualCost:F4}, {actualLatency.TotalSeconds:F1}s"
            
        | Error error ->
            printfn $"Provider selection failed: {error}"
        
        printfn ""
