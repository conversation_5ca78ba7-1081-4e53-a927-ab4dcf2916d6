namespace Automation.AI

open System
open System.IO
open System.Collections.Generic
open System.Text.Json
open Automation.Core

/// Auto-healing agent for failed automation actions
module AutoHealAgent =
    
    /// Context information collected when an action fails
    type FailureContext = {
        FailedAction: Action
        OriginalSelector: string
        ScreenshotPath: string option
        HtmlContent: string option
        CurrentUrl: string option
        ErrorMessage: string
        Timestamp: DateTimeOffset
        Platform: string // "web" or "mobile"
        AttemptedSelectors: string list
    }
    
    /// Proposed healing solution from AI
    type HealingSolution = {
        NewSelector: string
        SelectorType: string // "css", "xpath", "id", "text", "aria-label", etc.
        ConfidenceScore: float
        Reasoning: string
        EstimatedSuccessRate: float option
    }
    
    /// Result of healing attempt
    type HealingResult =
        | HealingSuccess of solution: HealingSolution * newAction: Action
        | HealingFailed of reason: string * solutionsAttempted: HealingSolution list
        | MaxHealsExceeded of attemptsUsed: int
        | ContextInsufficient of missingElements: string list
    
    /// Configuration for auto-healing
    type AutoHealConfig = {
        MaxHealAttempts: int
        MinConfidenceScore: float
        EnableScreenshotAnalysis: bool
        EnableDOMAnalysis: bool
        CreateNewVersionOnSuccess: bool
        HealingTimeoutMs: int
        PreferredSelectorTypes: string list
    }
    
    /// Default auto-heal configuration
    let defaultAutoHealConfig = {
        MaxHealAttempts = 3
        MinConfidenceScore = 0.8
        EnableScreenshotAnalysis = true
        EnableDOMAnalysis = true
        CreateNewVersionOnSuccess = true
        HealingTimeoutMs = 30000
        PreferredSelectorTypes = ["data-testid"; "aria-label"; "id"; "css"; "xpath"]
    }
    
    /// Extract selector from action
    let extractSelector (action: Action) =
        match action with
        | Click selector -> Some selector
        | Tap selector -> Some selector
        | TypeText (selector, _) -> Some selector
        | GetText selector -> Some selector
        | Navigate _ -> None
        | Screenshot _ -> None
    
    /// Create new action with updated selector
    let updateActionSelector (action: Action) (newSelector: string) =
        match action with
        | Click _ -> Click newSelector
        | Tap _ -> Tap newSelector
        | TypeText (_, text) -> TypeText (newSelector, text)
        | GetText _ -> GetText newSelector
        | Navigate url -> Navigate url // No change for navigate
        | Screenshot path -> Screenshot path // No change for screenshot
    
    /// Validate healing solution
    let validateHealingSolution (config: AutoHealConfig) (solution: HealingSolution) =
        if solution.ConfidenceScore < config.MinConfidenceScore then
            Error $"Confidence score {solution.ConfidenceScore} below minimum {config.MinConfidenceScore}"
        elif String.IsNullOrWhiteSpace(solution.NewSelector) then
            Error "New selector is empty or null"
        elif not (config.PreferredSelectorTypes |> List.contains solution.SelectorType) then
            let joinedTypes = String.Join(", ", config.PreferredSelectorTypes)
            Error $"Selector type '{solution.SelectorType}' not in preferred types: {joinedTypes}"
        else
            Ok solution
    
    /// Dedicated AutoHealAIProcessor module for enhanced prompt construction and response parsing
    module AutoHealAIProcessor =
        
        /// Convert screenshot to base64 for AI processing
        let convertScreenshotToBase64 (screenshotPath: string option) =
            match screenshotPath with
            | Some path when File.Exists(path) ->
                try
                    let bytes = File.ReadAllBytes(path)
                    Some (Convert.ToBase64String(bytes))
                with
                | _ -> None
            | _ -> None
        
        /// Extract relevant DOM snippet for healing context
        let extractRelevantDOM (htmlContent: string option) (originalSelector: string) =
            match htmlContent with
            | Some html when html.Length > 0 ->
                try
                    // Extract a focused section around the failed element's area
                    let maxLength = 2000 // Limit to avoid token overflow
                    if html.Length <= maxLength then
                        Some html
                    else
                        // Try to find context around the original selector
                        let selectorIndex = html.IndexOf(originalSelector, StringComparison.OrdinalIgnoreCase)
                        if selectorIndex >= 0 then
                            let start = max 0 (selectorIndex - 500)
                            let length = min maxLength (html.Length - start)
                            Some (html.Substring(start, length))
                        else
                            // Fallback: take first part of HTML
                            Some (html.Substring(0, maxLength) + "...[truncated]")
                with
                | _ -> None
            | _ -> None
        
        /// Build enhanced healing prompt with optimized format
        let buildEnhancedHealingPrompt (context: FailureContext) (config: AutoHealConfig) =
            let originalSelector = 
                match extractSelector context.FailedAction with
                | Some sel -> sel
                | None -> "N/A"
            
            let attemptedSelectorsText = 
                if context.AttemptedSelectors.IsEmpty then "None"
                else String.Join(", ", context.AttemptedSelectors)
            
            let domSnippet = extractRelevantDOM context.HtmlContent originalSelector
            let domSection = 
                match domSnippet with
                | Some snippet when config.EnableDOMAnalysis -> 
                    $"\nDOM_CONTEXT:\n```html\n{snippet}\n```"
                | _ -> "\nDOM_CONTEXT: Not available"
            
            let screenshotSection = 
                match convertScreenshotToBase64 context.ScreenshotPath with
                | Some base64 when config.EnableScreenshotAnalysis -> 
                    $"\nSCREENSHOT_DATA: {base64.Substring(0, min 100 base64.Length)}...[base64 encoded]"
                | _ -> "\nSCREENSHOT_DATA: Not available"
            
            let preferredTypes = String.Join(", ", config.PreferredSelectorTypes)
            
            $"""You are an expert automation healing agent. An element selector failed and you must propose a robust replacement.

FAILURE_ANALYSIS:
Platform: {context.Platform}
Failed_Action: {context.FailedAction}
Original_Selector: "{originalSelector}"
Error_Message: "{context.ErrorMessage}"
Current_URL: {defaultArg context.CurrentUrl "Unknown"}
Previously_Attempted: {attemptedSelectorsText}
Timestamp: {context.Timestamp.ToString("yyyy-MM-dd HH:mm:ss")}

{domSection}
{screenshotSection}

HEALING_REQUIREMENTS:
- Minimum confidence: {config.MinConfidenceScore:F2}
- Preferred selector types (in order): {preferredTypes}
- Focus on stable, maintainable selectors
- Avoid fragile selectors (nth-child, absolute positioning)

RESPONSE_FORMAT:
Return ONLY valid JSON in this exact format:
{{
  "newSelector": "string - the replacement selector",
  "selectorType": "string - css|xpath|id|text|aria-label|data-testid", 
  "confidenceScore": number - between 0.0 and 1.0,
  "reasoning": "string - why this selector will work",
  "estimatedSuccessRate": number - between 0.0 and 1.0
}}

Generate the optimal healing solution:"""
        
        /// Enhanced JSON parsing with multiple fallback strategies
        let parseHealingSolutionRobust (aiResponse: string) =
            let tryParseJson (jsonText: string) =
                try
                    let options = JsonSerializerOptions()
                    options.PropertyNameCaseInsensitive <- true
                    options.AllowTrailingCommas <- true
                    let solution = JsonSerializer.Deserialize<HealingSolution>(jsonText, options)
                    Some solution
                with
                | _ -> None
            
            // Strategy 1: Direct parsing
            match tryParseJson aiResponse with
            | Some solution -> Ok solution
            | None ->
                // Strategy 2: Extract JSON from response
                let jsonPattern = @"\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}"
                let regex = System.Text.RegularExpressions.Regex(jsonPattern, System.Text.RegularExpressions.RegexOptions.Singleline)
                let matches = regex.Matches(aiResponse)
                
                let mutable found = false
                let mutable result = Error "No valid JSON found"
                
                for i in 0 .. matches.Count - 1 do
                    if not found then
                        match tryParseJson matches.[i].Value with
                        | Some solution -> 
                            found <- true
                            result <- Ok solution
                        | None -> ()
                
                if found then result
                else
                    // Strategy 3: Manual field extraction as last resort
                    try
                        let extractField (fieldName: string) (text: string) =
                            let pattern = $@"""{fieldName}"":\s*""([^""]+)"""
                            let match' = System.Text.RegularExpressions.Regex.Match(text, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
                            if match'.Success then Some match'.Groups.[1].Value else None
                        
                        let extractNumber (fieldName: string) (text: string) =
                            let pattern = $@"""{fieldName}"":\s*([0-9.]+)"""
                            let match' = System.Text.RegularExpressions.Regex.Match(text, pattern, System.Text.RegularExpressions.RegexOptions.IgnoreCase)
                            if match'.Success then 
                                match Double.TryParse(match'.Groups.[1].Value) with
                                | true, value -> Some value
                                | false, _ -> None
                            else None
                        
                        let newSelector = extractField "newSelector" aiResponse
                        let selectorType = extractField "selectorType" aiResponse
                        let confidenceScore = extractNumber "confidenceScore" aiResponse
                        let reasoning = extractField "reasoning" aiResponse
                        let estimatedSuccessRate = extractNumber "estimatedSuccessRate" aiResponse
                        
                        match newSelector, selectorType, confidenceScore, reasoning with
                        | Some selector, Some selType, Some confidence, Some reason ->
                            Ok {
                                NewSelector = selector
                                SelectorType = selType
                                ConfidenceScore = confidence
                                Reasoning = reason
                                EstimatedSuccessRate = estimatedSuccessRate
                            }
                        | _ -> Error "Could not extract required fields from AI response"
                    with
                    | ex -> Error $"Manual parsing failed: {ex.Message}"
        
        /// Validate extracted healing solution with enhanced checks
        let validateEnhancedHealingSolution (config: AutoHealConfig) (solution: HealingSolution) =
            // Basic validation
            if solution.ConfidenceScore < config.MinConfidenceScore then
                Error $"Confidence score {solution.ConfidenceScore:F2} below minimum {config.MinConfidenceScore:F2}"
            elif String.IsNullOrWhiteSpace(solution.NewSelector) then
                Error "New selector is empty or null"
            elif solution.NewSelector.Length > 500 then
                Error "New selector is too long (>500 characters)"
            elif not (config.PreferredSelectorTypes |> List.contains solution.SelectorType) then
                let joinedTypes = String.Join(", ", config.PreferredSelectorTypes)
                Error $"Selector type '{solution.SelectorType}' not in preferred types: {joinedTypes}"
            else
                // Advanced validation checks
                let isValidSelector = 
                    match solution.SelectorType.ToLower() with
                    | "css" -> 
                        // Basic CSS selector validation
                        not (solution.NewSelector.Contains("..") || solution.NewSelector.Contains("//"))
                    | "xpath" ->
                        // Basic XPath validation
                        solution.NewSelector.StartsWith("/") || solution.NewSelector.StartsWith("//")
                    | "id" ->
                        // ID should not contain spaces or special chars
                        not (solution.NewSelector.Contains(" ") || solution.NewSelector.Contains("#"))
                    | "data-testid" ->
                        // Should be a valid attribute value
                        not (String.IsNullOrWhiteSpace(solution.NewSelector))
                    | "aria-label" ->
                        // Should be non-empty text
                        not (String.IsNullOrWhiteSpace(solution.NewSelector))
                    | "text" ->
                        // Should be meaningful text
                        solution.NewSelector.Length >= 2
                    | _ -> true // Allow other types for now
                
                if not isValidSelector then
                    Error $"Invalid selector format for type '{solution.SelectorType}': {solution.NewSelector}"
                else
                    Ok solution

    /// Build healing prompt for AI (legacy method, kept for compatibility)
    let buildHealingPrompt (context: FailureContext) (config: AutoHealConfig) =
        AutoHealAIProcessor.buildEnhancedHealingPrompt context config
    
    /// Collect failure context from task result
    let collectFailureContext (executionError: ActionExecutionError) (platform: string) =
        match executionError.FailedAction with
        | Some action ->
            let selector = extractSelector action |> Option.defaultValue ""
            Ok {
                FailedAction = action
                OriginalSelector = selector
                ScreenshotPath = executionError.ScreenshotPath
                HtmlContent = executionError.HtmlContent
                CurrentUrl = executionError.CurrentUrl
                ErrorMessage = executionError.Message
                Timestamp = DateTimeOffset.Now
                Platform = platform
                AttemptedSelectors = executionError.AttemptedSelectors
            }
        | None ->
            Error "No failed action available in execution error"
    
    /// Parse AI response to healing solution
    let parseHealingSolution (aiResponse: string) =
        try
            let options = JsonSerializerOptions()
            options.PropertyNameCaseInsensitive <- true
            
            let solution = JsonSerializer.Deserialize<HealingSolution>(aiResponse, options)
            Ok solution
        with
        | ex -> Error $"Failed to parse AI response: {ex.Message}"
    
    /// Auto-heal manager
    type AutoHealManager(config: AutoHealConfig, aiProvider: AIFramework.IAIProvider option, auditLogger: AuditLogger.AuditLogger option) =
        let healingAttempts = Dictionary<string, int>()
        
        /// Get unique key for action to track healing attempts
        let getActionKey (action: Action) =
            match action with
            | Click selector -> $"click:{selector}"
            | Tap selector -> $"tap:{selector}"
            | TypeText (selector, text) -> $"type:{selector}:{text}"
            | GetText selector -> $"get:{selector}"
            | Navigate url -> $"nav:{url}"
            | Screenshot path -> $"screenshot:{path}"
        
        /// Check if max heals exceeded for this action
        member _.IsMaxHealsExceeded(action: Action) =
            let key = getActionKey action
            match healingAttempts.TryGetValue(key) with
            | true, count -> count >= config.MaxHealAttempts
            | false, _ -> false
        
        /// Increment healing attempt counter
        member _.IncrementHealingAttempt(action: Action) =
            let key = getActionKey action
            match healingAttempts.TryGetValue(key) with
            | true, count -> healingAttempts.[key] <- count + 1
            | false, _ -> healingAttempts.[key] <- 1
        
        /// Attempt to heal a failed action
        member this.AttemptHealing(failureContext: FailureContext) =
            async {
                // Check if max heals exceeded
                if this.IsMaxHealsExceeded(failureContext.FailedAction) then
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Error,
                            AuditLogger.Warning,
                            "AutoHealAgent",
                            $"Max healing attempts exceeded for action: {failureContext.FailedAction}",
                            Map.ofList [
                                ("action", box (failureContext.FailedAction.ToString()))
                                ("maxAttempts", box config.MaxHealAttempts)
                                ("platform", box failureContext.Platform)
                            ]
                        )
                    )
                    return MaxHealsExceeded(config.MaxHealAttempts)
                else
                    // Increment attempt counter
                    this.IncrementHealingAttempt(failureContext.FailedAction)
                    
                    // Check if we have sufficient context
                    let missingElements = []
                    let missingElements = 
                        if config.EnableScreenshotAnalysis && failureContext.ScreenshotPath.IsNone then 
                            "screenshot" :: missingElements 
                        else missingElements
                    let missingElements = 
                        if config.EnableDOMAnalysis && failureContext.HtmlContent.IsNone then 
                            "domContent" :: missingElements 
                        else missingElements
                    
                    if not missingElements.IsEmpty then
                        return ContextInsufficient(missingElements)
                    else
                        match aiProvider with
                        | None -> 
                            return HealingFailed("No AI provider available for healing", [])
                        | Some provider ->
                            try
                                // Build enhanced healing prompt
                                let prompt = AutoHealAIProcessor.buildEnhancedHealingPrompt failureContext config
                                
                                // Create AI request
                                let aiRequest = {
                                    Command = prompt
                                    SystemMessage = Some "You are an expert automation healing agent. Analyze failures and propose robust selector solutions."
                                    Images = None
                                    MaxTokens = Some 1000
                                    Temperature = Some 0.1
                                    Model = None
                                    Metadata = Some Map.empty
                                }
                                
                                auditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.AIRequest,
                                        AuditLogger.Info,
                                        "AutoHealAgent",
                                        "Requesting healing solution from AI",
                                        Map.ofList [
                                            ("action", box (failureContext.FailedAction.ToString()))
                                            ("originalSelector", box failureContext.OriginalSelector)
                                            ("platform", box failureContext.Platform)
                                            ("errorMessage", box failureContext.ErrorMessage)
                                        ]
                                    )
                                )
                                
                                // Call AI provider
                                let! aiResponse = provider.GenerateResponse(aiRequest)
                                
                                match aiResponse with
                                | Ok response ->
                                    // Parse healing solution using enhanced parsing
                                    match AutoHealAIProcessor.parseHealingSolutionRobust response.Content with
                                    | Ok solution ->
                                        // Validate solution using enhanced validation
                                        match AutoHealAIProcessor.validateEnhancedHealingSolution config solution with
                                        | Ok validSolution ->
                                            // Create new action with updated selector
                                            let newAction = updateActionSelector failureContext.FailedAction validSolution.NewSelector
                                            
                                            auditLogger |> Option.iter (fun logger ->
                                                logger.LogEvent(
                                                    AuditLogger.AIResponse,
                                                    AuditLogger.Info,
                                                    "AutoHealAgent",
                                                    "Healing solution generated successfully",
                                                    Map.ofList [
                                                        ("originalSelector", box failureContext.OriginalSelector)
                                                        ("newSelector", box validSolution.NewSelector)
                                                        ("confidenceScore", box validSolution.ConfidenceScore)
                                                        ("selectorType", box validSolution.SelectorType)
                                                        ("reasoning", box validSolution.Reasoning)
                                                    ]
                                                )
                                            )
                                            
                                            return HealingSuccess(validSolution, newAction)
                                        | Error validationError ->
                                            return HealingFailed($"Solution validation failed: {validationError}", [solution])
                                    | Error parseError ->
                                        return HealingFailed($"Failed to parse AI response: {parseError}", [])
                                | Error aiError ->
                                    return HealingFailed($"AI provider error: {aiError}", [])
                            with
                            | ex ->
                                auditLogger |> Option.iter (fun logger ->
                                    logger.LogEvent(
                                        AuditLogger.Error,
                                        AuditLogger.Error,
                                        "AutoHealAgent",
                                        $"Healing attempt failed with exception: {ex.Message}",
                                        Map.ofList [
                                            ("action", box (failureContext.FailedAction.ToString()))
                                            ("exception", box ex.Message)
                                            ("stackTrace", box ex.StackTrace)
                                        ]
                                    )
                                )
                                return HealingFailed($"Exception during healing: {ex.Message}", [])
            }
        
        /// Reset healing attempts for an action (call after successful execution)
        member _.ResetHealingAttempts(action: Action) =
            let key = getActionKey action
            healingAttempts.Remove(key) |> ignore
        
        /// Get healing statistics
        member _.GetHealingStatistics() =
            {|
                TotalActionsWithAttempts = healingAttempts.Count
                AverageAttemptsPerAction = 
                    if healingAttempts.Count > 0 then
                        healingAttempts.Values |> Seq.map float |> Seq.average
                    else 0.0
                MaxAttemptsUsed = 
                    if healingAttempts.Count > 0 then
                        healingAttempts.Values |> Seq.max
                    else 0
                ActionsAtMaxAttempts = 
                    healingAttempts.Values |> Seq.filter (fun count -> count >= config.MaxHealAttempts) |> Seq.length
            |}
        
        /// Clear all healing attempt history
        member _.ClearHealingHistory() =
            healingAttempts.Clear()
    
    /// Dynamic Action Retry and Healing Logic Component
    module HealingRetryLogic =
        
        /// Action execution function signature for retry logic
        type ActionExecutor<'T> = Action -> Async<Result<'T, ActionExecutionError>>
        
        /// Healing retry context
        type HealingRetryContext<'T> = {
            OriginalAction: Action
            Executor: ActionExecutor<'T>
            HealingManager: AutoHealManager
            Config: AutoHealConfig
            Platform: string
        }
        
        /// Result of healing retry attempt
        type HealingRetryResult<'T> =
            | RetrySuccess of result: 'T * healingAttempts: int * finalAction: Action
            | RetryFailed of lastError: ActionExecutionError * healingAttempts: int * allSolutions: HealingSolution list
            | MaxHealsExceeded of attempts: int
            | HealingError of reason: string
        
        /// Execute action with automatic healing retry logic
        let executeWithHealingRetry<'T> (context: HealingRetryContext<'T>) : Async<HealingRetryResult<'T>> =
            async {
                let mutable currentAction = context.OriginalAction
                let mutable healingAttempts = 0
                let mutable allSolutions = []
                let mutable lastError = None
                let mutable continueRetrying = true
                
                while continueRetrying do
                    // Execute the current action
                    let! executionResult = context.Executor currentAction
                    
                    match executionResult with
                    | Ok result ->
                        // Success! Reset healing attempts for this action and return
                        context.HealingManager.ResetHealingAttempts(context.OriginalAction)
                        return RetrySuccess(result, healingAttempts, currentAction)
                    | Error actionError ->
                        lastError <- Some actionError
                        
                        // Check if we've exceeded max healing attempts
                        if context.HealingManager.IsMaxHealsExceeded(context.OriginalAction) then
                            continueRetrying <- false
                        else
                            // Attempt to heal the action
                            match collectFailureContext actionError context.Platform with
                            | Ok failureContext ->
                                let! healingResult = context.HealingManager.AttemptHealing(failureContext)
                                
                                match healingResult with
                                | HealingSuccess (solution, newAction) ->
                                    // Healing was successful, update action for retry
                                    healingAttempts <- healingAttempts + 1
                                    allSolutions <- solution :: allSolutions
                                    currentAction <- newAction
                                    // Continue loop for retry
                                | HealingFailed (reason, solutions) ->
                                    allSolutions <- solutions @ allSolutions
                                    continueRetrying <- false
                                | MaxHealsExceeded attempts ->
                                    return MaxHealsExceeded attempts
                                | ContextInsufficient missingElements ->
                                    return HealingError $"""Insufficient context for healing: {String.Join(", ", missingElements)}"""
                            | Error contextError ->
                                return HealingError $"Failed to collect failure context: {contextError}"
                
                // Return final failure result
                match lastError with
                | Some error -> return RetryFailed(error, healingAttempts, allSolutions)
                | None -> return HealingError "No execution attempted"
            }
        
        /// Simplified version that returns the original Result type for easier integration
        let executeWithHealingRetrySimple<'T> (context: HealingRetryContext<'T>) : Async<Result<'T, ActionExecutionError>> =
            async {
                let! healingResult = executeWithHealingRetry context
                
                match healingResult with
                | RetrySuccess (result, _, _) -> return Ok result
                | RetryFailed (lastError, _, _) -> return Error lastError
                | MaxHealsExceeded _ -> 
                    return Error {
                        Message = $"Maximum healing attempts ({context.Config.MaxHealAttempts}) exceeded for action"
                        FailedAction = Some context.OriginalAction
                        ScreenshotPath = None
                        HtmlContent = None
                        CurrentUrl = None
                        AttemptedSelectors = []
                        Timestamp = DateTimeOffset.Now
                    }
                | HealingError reason ->
                    return Error {
                        Message = $"Healing error: {reason}"
                        FailedAction = Some context.OriginalAction
                        ScreenshotPath = None
                        HtmlContent = None
                        CurrentUrl = None
                        AttemptedSelectors = []
                        Timestamp = DateTimeOffset.Now
                    }
            }
        
        /// Create healing retry context for easier integration
        let createHealingContext<'T> (action: Action) (executor: ActionExecutor<'T>) (healingManager: AutoHealManager) (config: AutoHealConfig) (platform: string) =
            {
                OriginalAction = action
                Executor = executor
                HealingManager = healingManager
                Config = config
                Platform = platform
            }
        
        /// Statistics about healing retry execution
        type HealingRetryStats = {
            OriginalAction: Action
            FinalAction: Action option
            HealingAttempts: int
            AllSolutions: HealingSolution list
            TotalExecutionTime: TimeSpan
            Successful: bool
            FailureReason: string option
        }
        
        /// Execute with detailed statistics tracking
        let executeWithStats<'T> (context: HealingRetryContext<'T>) : Async<Result<'T, ActionExecutionError> * HealingRetryStats> =
            async {
                let startTime = DateTimeOffset.Now
                let! healingResult = executeWithHealingRetry context
                let endTime = DateTimeOffset.Now
                let executionTime = endTime - startTime
                
                let (result, stats) = 
                    match healingResult with
                    | RetrySuccess (successResult, attempts, finalAction) ->
                        let successStats = {
                            OriginalAction = context.OriginalAction
                            FinalAction = Some finalAction
                            HealingAttempts = attempts
                            AllSolutions = []
                            TotalExecutionTime = executionTime
                            Successful = true
                            FailureReason = None
                        }
                        (Ok successResult, successStats)
                    | RetryFailed (error, attempts, solutions) ->
                        let failureStats = {
                            OriginalAction = context.OriginalAction
                            FinalAction = None
                            HealingAttempts = attempts
                            AllSolutions = solutions
                            TotalExecutionTime = executionTime
                            Successful = false
                            FailureReason = Some error.Message
                        }
                        (Error error, failureStats)
                    | MaxHealsExceeded attempts ->
                        let maxStats = {
                            OriginalAction = context.OriginalAction
                            FinalAction = None
                            HealingAttempts = attempts
                            AllSolutions = []
                            TotalExecutionTime = executionTime
                            Successful = false
                            FailureReason = Some $"Maximum healing attempts ({attempts}) exceeded"
                        }
                        let error = {
                            Message = $"Maximum healing attempts ({attempts}) exceeded"
                            FailedAction = Some context.OriginalAction
                            ScreenshotPath = None
                            HtmlContent = None
                            CurrentUrl = None
                            AttemptedSelectors = []
                            Timestamp = DateTimeOffset.Now
                        }
                        (Error error, maxStats)
                    | HealingError reason ->
                        let errorStats = {
                            OriginalAction = context.OriginalAction
                            FinalAction = None
                            HealingAttempts = 0
                            AllSolutions = []
                            TotalExecutionTime = executionTime
                            Successful = false
                            FailureReason = Some reason
                        }
                        let error = {
                            Message = reason
                            FailedAction = Some context.OriginalAction
                            ScreenshotPath = None
                            HtmlContent = None
                            CurrentUrl = None
                            AttemptedSelectors = []
                            Timestamp = DateTimeOffset.Now
                        }
                        (Error error, errorStats)
                
                return (result, stats)
            }

    /// Helper functions for integration with executors
    module ExecutorIntegration =
        
        /// Enhanced failure result with healing context
        type EnhancedFailureResult = {
            OriginalFailure: ActionExecutionError
            HealingContext: FailureContext option
            HealingSolutions: HealingSolution list
            HealingAttempts: int
        }
        
        /// Convert standard failure to enhanced failure
        let enhanceFailureResult (platform: string) (executionError: ActionExecutionError) =
            let healingContext = collectFailureContext executionError platform
            {
                OriginalFailure = executionError
                HealingContext = healingContext |> Result.toOption
                HealingSolutions = []
                HealingAttempts = 0
            }
        
        /// Check if failure is healable
        let isHealableFailure (executionError: ActionExecutionError) =
            executionError.FailedAction.IsSome &&
            (executionError.ScreenshotPath.IsSome || executionError.HtmlContent.IsSome)

    /// Healing-enabled automation executors
    module HealingExecutors =
        
        open HealingRetryLogic
        
        /// Configuration for healing-enabled executors
        type HealingExecutorConfig = {
            AutoHealConfig: AutoHealConfig
            EnableHealing: bool
            Platform: string // "web" or "mobile"
            HealingManager: AutoHealManager option
            AuditLogger: AuditLogger.AuditLogger option
        }
        
        /// Default healing configuration for web
        let defaultWebHealingConfig (aiProvider: AIFramework.IAIProvider option) (auditLogger: AuditLogger.AuditLogger option) = {
            AutoHealConfig = defaultAutoHealConfig
            EnableHealing = true
            Platform = "web"
            HealingManager = 
                match aiProvider with
                | Some provider -> Some (new AutoHealManager(defaultAutoHealConfig, Some provider, auditLogger))
                | None -> None
            AuditLogger = auditLogger
        }
        
        /// Default healing configuration for mobile
        let defaultMobileHealingConfig (aiProvider: AIFramework.IAIProvider option) (auditLogger: AuditLogger.AuditLogger option) = {
            AutoHealConfig = defaultAutoHealConfig
            EnableHealing = true
            Platform = "mobile"
            HealingManager = 
                match aiProvider with
                | Some provider -> Some (new AutoHealManager(defaultAutoHealConfig, Some provider, auditLogger))
                | None -> None
            AuditLogger = auditLogger
        }
        
        /// Healing-enabled web task executor
        type HealingWebTaskExecutor(healingConfig: HealingExecutorConfig, baseExecutor: ITaskExecutor) =
            
            /// Execute single action with healing support
            let executeSingleActionWithHealing (action: Action) : Async<Result<TaskResult, ActionExecutionError>> =
                async {
                    if not healingConfig.EnableHealing || healingConfig.HealingManager.IsNone then
                        // No healing enabled, use base executor
                        let! result = baseExecutor.ExecuteActions([action])
                        return 
                            match result with
                            | Success msg -> Ok result
                            | Failure error -> Error error
                    else
                        // Healing enabled
                        let healingManager = healingConfig.HealingManager.Value
                        
                        // Define the action executor function
                        let actionExecutor (singleAction: Action) : Async<Result<TaskResult, ActionExecutionError>> =
                            async {
                                let! result = baseExecutor.ExecuteActions([singleAction])
                                return 
                                    match result with
                                    | Success msg -> Ok result
                                    | Failure error -> Error error
                            }
                        
                        // Create healing context
                        let context = createHealingContext action actionExecutor healingManager healingConfig.AutoHealConfig healingConfig.Platform
                        
                        // Execute with healing retry
                        return! executeWithHealingRetrySimple context
                }
            
            interface ITaskExecutor with
                member _.ExecuteActions(actions) =
                    async {
                        if List.isEmpty actions then
                            return Success "No actions to execute"
                        else
                            let mutable allSuccessful = true
                            let mutable combinedMessages = []
                            let mutable lastError = None
                            
                            for action in actions do
                                if allSuccessful then
                                    let! result = executeSingleActionWithHealing action
                                    match result with
                                    | Ok (Success msg) ->
                                        combinedMessages <- msg :: combinedMessages
                                    | Ok (Failure error) ->
                                        allSuccessful <- false
                                        lastError <- Some error
                                    | Error error ->
                                        allSuccessful <- false
                                        lastError <- Some error
                            
                            if allSuccessful then
                                let finalMessage = String.Join("; ", List.rev combinedMessages)
                                return Success finalMessage
                            else
                                match lastError with
                                | Some error -> return Failure error
                                | None -> return Failure {
                                    Message = "Unknown error during healing execution"
                                    FailedAction = None
                                    ScreenshotPath = None
                                    HtmlContent = None
                                    CurrentUrl = None
                                    AttemptedSelectors = []
                                    Timestamp = DateTimeOffset.Now
                                }
                    }
                
                member _.TakeScreenshot(path) = baseExecutor.TakeScreenshot(path)
                member _.GetCurrentUrl() = baseExecutor.GetCurrentUrl()
                member _.GetCurrentHtmlContent() = baseExecutor.GetCurrentHtmlContent()
            
            interface IDisposable with
                member _.Dispose() =
                    if baseExecutor :? IDisposable then
                        (baseExecutor :?> IDisposable).Dispose()
        
        /// Healing-enabled mobile task executor
        type HealingMobileTaskExecutor(healingConfig: HealingExecutorConfig, baseExecutor: ITaskExecutor) =
            
            /// Execute single action with healing support for mobile
            let executeSingleActionWithHealing (action: Action) : Async<Result<TaskResult, ActionExecutionError>> =
                async {
                    if not healingConfig.EnableHealing || healingConfig.HealingManager.IsNone then
                        // No healing enabled, use base executor
                        let! result = baseExecutor.ExecuteActions([action])
                        return 
                            match result with
                            | Success msg -> Ok result
                            | Failure error -> Error error
                    else
                        // Healing enabled
                        let healingManager = healingConfig.HealingManager.Value
                        
                        // Define the action executor function for mobile
                        let actionExecutor (singleAction: Action) : Async<Result<TaskResult, ActionExecutionError>> =
                            async {
                                let! result = baseExecutor.ExecuteActions([singleAction])
                                return 
                                    match result with
                                    | Success msg -> Ok result
                                    | Failure error -> Error error
                            }
                        
                        // Create healing context for mobile
                        let context = createHealingContext action actionExecutor healingManager healingConfig.AutoHealConfig healingConfig.Platform
                        
                        // Execute with healing retry
                        return! executeWithHealingRetrySimple context
                }
            
            interface ITaskExecutor with
                member _.ExecuteActions(actions) =
                    async {
                        if List.isEmpty actions then
                            return Success "No mobile actions to execute"
                        else
                            let mutable allSuccessful = true
                            let mutable combinedMessages = []
                            let mutable lastError = None
                            
                            for action in actions do
                                if allSuccessful then
                                    let! result = executeSingleActionWithHealing action
                                    match result with
                                    | Ok (Success msg) ->
                                        combinedMessages <- msg :: combinedMessages
                                    | Ok (Failure error) ->
                                        allSuccessful <- false
                                        lastError <- Some error
                                    | Error error ->
                                        allSuccessful <- false
                                        lastError <- Some error
                            
                            if allSuccessful then
                                let finalMessage = String.Join("; ", List.rev combinedMessages)
                                return Success finalMessage
                            else
                                match lastError with
                                | Some error -> return Failure error
                                | None -> return Failure {
                                    Message = "Unknown error during mobile healing execution"
                                    FailedAction = None
                                    ScreenshotPath = None
                                    HtmlContent = None
                                    CurrentUrl = None
                                    AttemptedSelectors = []
                                    Timestamp = DateTimeOffset.Now
                                }
                    }
                
                member _.TakeScreenshot(path) = baseExecutor.TakeScreenshot(path)
                member _.GetCurrentUrl() = baseExecutor.GetCurrentUrl()
                member _.GetCurrentHtmlContent() = baseExecutor.GetCurrentHtmlContent()
            
            interface IDisposable with
                member _.Dispose() =
                    if baseExecutor :? IDisposable then
                        (baseExecutor :?> IDisposable).Dispose()
        
        /// Factory functions for creating healing-enabled executors
        module Factory =
            
            /// Create healing-enabled web executor
            let createHealingWebExecutor (baseExecutor: ITaskExecutor) (aiProvider: AIFramework.IAIProvider option) (auditLogger: AuditLogger.AuditLogger option) =
                let healingConfig = defaultWebHealingConfig aiProvider auditLogger
                new HealingWebTaskExecutor(healingConfig, baseExecutor) :> ITaskExecutor
            
            /// Create healing-enabled mobile executor
            let createHealingMobileExecutor (baseExecutor: ITaskExecutor) (aiProvider: AIFramework.IAIProvider option) (auditLogger: AuditLogger.AuditLogger option) =
                let healingConfig = defaultMobileHealingConfig aiProvider auditLogger
                new HealingMobileTaskExecutor(healingConfig, baseExecutor) :> ITaskExecutor
            
            /// Create healing-enabled executor with custom configuration
            let createHealingExecutorWithConfig (baseExecutor: ITaskExecutor) (healingConfig: HealingExecutorConfig) =
                match healingConfig.Platform.ToLower() with
                | "web" -> new HealingWebTaskExecutor(healingConfig, baseExecutor) :> ITaskExecutor
                | "mobile" -> new HealingMobileTaskExecutor(healingConfig, baseExecutor) :> ITaskExecutor
                | _ -> failwith $"Unsupported platform: {healingConfig.Platform}"

    /// CreateNewVersion functionality for persisting successful healing results
    module VersionManagement =
        
        /// Version update strategy
        type VersionUpdateStrategy =
            | DatabaseUpdate of connectionString: string
            | FileUpdate of filePath: string
            | AuditLogOnly
            | CustomCallback of (Action * Action * HealingSolution -> Async<Result<string, string>>)
        
        /// Version update record
        type VersionUpdate = {
            Id: Guid
            OriginalAction: Action
            HealedAction: Action
            HealingSolution: HealingSolution
            Platform: string
            Timestamp: DateTimeOffset
            TestCaseId: string option
            ScriptPath: string option
            Success: bool
            Notes: string option
        }
        
        /// Version manager for handling createNewVersion functionality
        type VersionManager(strategy: VersionUpdateStrategy, auditLogger: AuditLogger.AuditLogger option) =
            
            /// Create new version record
            member _.CreateNewVersion(originalAction: Action, healedAction: Action, solution: HealingSolution, platform: string, ?testCaseId: string, ?scriptPath: string, ?notes: string) =
                async {
                    let versionUpdate = {
                        Id = Guid.NewGuid()
                        OriginalAction = originalAction
                        HealedAction = healedAction
                        HealingSolution = solution
                        Platform = platform
                        Timestamp = DateTimeOffset.Now
                        TestCaseId = testCaseId
                        ScriptPath = scriptPath
                        Success = false // Will be updated after persistence
                        Notes = notes
                    }
                    
                    auditLogger |> Option.iter (fun logger ->
                        logger.LogEvent(
                            AuditLogger.Info,
                            AuditLogger.Info,
                            "VersionManager",
                            "Creating new version after successful healing",
                            Map.ofList [
                                ("versionId", box versionUpdate.Id)
                                ("originalSelector", box (extractSelector originalAction |> Option.defaultValue "N/A"))
                                ("newSelector", box solution.NewSelector)
                                ("confidenceScore", box solution.ConfidenceScore)
                                ("platform", box platform)
                            ]
                        )
                    )
                    
                    try
                        let! result = __.PersistVersion(versionUpdate)
                        match result with
                        | Ok message ->
                            auditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.Info,
                                    AuditLogger.Info,
                                    "VersionManager",
                                    $"Successfully persisted version update: {message}",
                                    Map.ofList [("versionId", box versionUpdate.Id)]
                                )
                            )
                            return Ok { versionUpdate with Success = true }
                        | Error error ->
                            auditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.Error,
                                    AuditLogger.Error,
                                    "VersionManager",
                                    $"Failed to persist version update: {error}",
                                    Map.ofList [("versionId", box versionUpdate.Id)]
                                )
                            )
                            return Error error
                    with
                    | ex ->
                        let errorMsg = $"Exception during version persistence: {ex.Message}"
                        auditLogger |> Option.iter (fun logger ->
                            logger.LogEvent(
                                AuditLogger.Error,
                                AuditLogger.Error,
                                "VersionManager",
                                errorMsg,
                                Map.ofList [
                                    ("versionId", box versionUpdate.Id)
                                    ("exception", box ex.Message)
                                ]
                            )
                        )
                        return Error errorMsg
                }
            
            /// Persist version update based on strategy
            member _.PersistVersion(versionUpdate: VersionUpdate) =
                async {
                    match strategy with
                    | DatabaseUpdate connectionString ->
                        // Simulate database update (would use actual DB in production)
                        return Ok $"Database updated with version {versionUpdate.Id}"
                    
                    | FileUpdate filePath ->
                        try
                            let versionJson = System.Text.Json.JsonSerializer.Serialize(versionUpdate, System.Text.Json.JsonSerializerOptions(WriteIndented = true))
                            let timestampedPath = $"{filePath}_{versionUpdate.Timestamp:yyyyMMdd_HHmmss}.json"
                            do! File.WriteAllTextAsync(timestampedPath, versionJson) |> Async.AwaitTask
                            return Ok $"Version saved to file: {timestampedPath}"
                        with
                        | ex -> return Error $"File update failed: {ex.Message}"
                    
                    | AuditLogOnly ->
                        // Only log to audit system
                        return Ok "Version logged to audit system only"
                    
                    | CustomCallback callback ->
                        return! callback(versionUpdate.OriginalAction, versionUpdate.HealedAction, versionUpdate.HealingSolution)
                }
            
            /// Get version history
            member _.GetVersionHistory() =
                async {
                    // Placeholder - would retrieve from actual storage
                    return []
                }
        
        /// Enhanced healing result with version management
        type EnhancedHealingResult =
            | HealingSuccessWithVersion of solution: HealingSolution * newAction: Action * versionUpdate: VersionUpdate
            | HealingFailedWithAttempts of reason: string * solutionsAttempted: HealingSolution list * attempts: int
            | MaxHealsExceededFinal of attemptsUsed: int
            | ContextInsufficientFinal of missingElements: string list
        
        /// Enhanced auto-heal manager with version management
        type EnhancedAutoHealManager(config: AutoHealConfig, aiProvider: AIFramework.IAIProvider option, auditLogger: AuditLogger.AuditLogger option, versionManager: VersionManager option) =
            inherit AutoHealManager(config, aiProvider, auditLogger)
            
            /// Attempt healing with version management
            member this.AttemptHealingWithVersioning(failureContext: FailureContext) =
                async {
                    let! baseResult = base.AttemptHealing(failureContext)
                    
                    match baseResult with
                    | HealingSuccess (solution, newAction) when config.CreateNewVersionOnSuccess && versionManager.IsSome ->
                        // Create new version after successful healing
                        let versionMgr = versionManager.Value
                        let! versionResult = versionMgr.CreateNewVersion(
                            failureContext.FailedAction, 
                            newAction, 
                            solution, 
                            failureContext.Platform,
                            notes = Some $"Auto-healing successful with confidence {solution.ConfidenceScore:F2}"
                        )
                        
                        match versionResult with
                        | Ok versionUpdate ->
                            return HealingSuccessWithVersion(solution, newAction, versionUpdate)
                        | Error versionError ->
                            // Healing succeeded but versioning failed - still return success but log error
                            auditLogger |> Option.iter (fun logger ->
                                logger.LogEvent(
                                    AuditLogger.Warning,
                                    AuditLogger.Warning,
                                    "EnhancedAutoHealManager",
                                    $"Healing succeeded but version creation failed: {versionError}",
                                    Map.ofList [("originalResult", box "HealingSuccess")]
                                )
                            )
                            // Convert to base result format since versioning failed
                            return HealingSuccessWithVersion(solution, newAction, {
                                Id = Guid.NewGuid()
                                OriginalAction = failureContext.FailedAction
                                HealedAction = newAction
                                HealingSolution = solution
                                Platform = failureContext.Platform
                                Timestamp = DateTimeOffset.Now
                                TestCaseId = None
                                ScriptPath = None
                                Success = false
                                Notes = Some $"Version creation failed: {versionError}"
                            })
                    
                    | HealingSuccess (solution, newAction) ->
                        // No versioning enabled or no version manager
                        let dummyVersion = {
                            Id = Guid.NewGuid()
                            OriginalAction = failureContext.FailedAction
                            HealedAction = newAction
                            HealingSolution = solution
                            Platform = failureContext.Platform
                            Timestamp = DateTimeOffset.Now
                            TestCaseId = None
                            ScriptPath = None
                            Success = true
                            Notes = Some "Versioning not enabled"
                        }
                        return HealingSuccessWithVersion(solution, newAction, dummyVersion)
                    
                    | HealingFailed (reason, solutions) ->
                        return HealingFailedWithAttempts(reason, solutions, base.GetHealingStatistics().TotalActionsWithAttempts)
                    
                    | MaxHealsExceeded attempts ->
                        return MaxHealsExceededFinal(attempts)
                    
                    | ContextInsufficient missingElements ->
                        return ContextInsufficientFinal(missingElements)
                }

    /// Comprehensive testing framework for auto-heal agent
    module Testing =
        
        /// Test scenario definition
        type TestScenario = {
            Name: string
            Description: string
            Platform: string // "web" or "mobile"
            OriginalAction: Action
            ExpectedFailure: string
            ExpectedHealingSolution: HealingSolution option
            ShouldSucceedAfterHealing: bool
            MaxHealsToTest: int
        }
        
        /// Test result
        type TestResult = {
            Scenario: TestScenario
            Success: bool
            ActualResult: string
            HealingAttempts: int
            HealingSolutions: HealingSolution list
            ExecutionTime: TimeSpan
            ErrorMessages: string list
        }
        
        /// Mock action executor for testing
        type MockActionExecutor(failurePattern: Action -> bool, healingPattern: Action -> bool) =
            member _.Execute(action: Action) : Async<Result<TaskResult, ActionExecutionError>> =
                async {
                    if failurePattern action then
                        return Error {
                            Message = "Mock failure - element not found"
                            FailedAction = Some action
                            ScreenshotPath = Some "mock_screenshot.png"
                            HtmlContent = Some "<html><body>Mock HTML content</body></html>"
                            CurrentUrl = Some "https://mock.example.com"
                            AttemptedSelectors = []
                            Timestamp = DateTimeOffset.Now
                        }
                    elif healingPattern action then
                        return Ok (Success "Mock healing success")
                    else
                        return Error {
                            Message = "Mock failure - healing also failed"
                            FailedAction = Some action
                            ScreenshotPath = Some "mock_screenshot_healing_failed.png"
                            HtmlContent = Some "<html><body>Mock HTML content - healing failed</body></html>"
                            CurrentUrl = Some "https://mock.example.com"
                            AttemptedSelectors = []
                            Timestamp = DateTimeOffset.Now
                        }
                }
        
        /// Mock AI provider for testing
        type MockAIProvider(responses: Map<string, string>) =
            interface AIFramework.IAIProvider with
                member _.GenerateResponse(request: AIFramework.AIRequest) =
                    async {
                        // Simple pattern matching for test responses
                        let mockResponse = 
                            if request.Prompt.Contains("element not found") then
                                """{"newSelector": "[data-testid='healed-button']", "selectorType": "css", "confidenceScore": 0.85, "reasoning": "Using data-testid attribute for better stability", "estimatedSuccessRate": 0.90}"""
                            else
                                """{"newSelector": ".healed-selector", "selectorType": "css", "confidenceScore": 0.75, "reasoning": "Generic healed selector", "estimatedSuccessRate": 0.80}"""
                        
                        return Ok {
                            Content = mockResponse
                            TokenUsage = Some 150
                            Model = Some "mock-model"
                            ProviderId = AIFramework.OpenAI
                            Cost = Some 0.001m
                            RequestId = Some (Guid.NewGuid().ToString())
                            Metadata = Map.empty
                        }
                    }
        
        /// Test suite for auto-heal agent
        type AutoHealTestSuite() =
            
            /// Generate test scenarios
            member _.GenerateTestScenarios() = [
                {
                    Name = "WebClickElementNotFound"
                    Description = "Test web click action healing when element is not found"
                    Platform = "web"
                    OriginalAction = Click "#non-existent-button"
                    ExpectedFailure = "element not found"
                    ExpectedHealingSolution = Some {
                        NewSelector = "[data-testid='healed-button']"
                        SelectorType = "css"
                        ConfidenceScore = 0.85
                        Reasoning = "Using data-testid attribute for better stability"
                        EstimatedSuccessRate = Some 0.90
                    }
                    ShouldSucceedAfterHealing = true
                    MaxHealsToTest = 3
                }
                {
                    Name = "MobileTapElementNotFound"
                    Description = "Test mobile tap action healing when element is not found"
                    Platform = "mobile"
                    OriginalAction = Tap "id:non-existent-element"
                    ExpectedFailure = "element not found"
                    ExpectedHealingSolution = Some {
                        NewSelector = "id:healed-element"
                        SelectorType = "id"
                        ConfidenceScore = 0.80
                        Reasoning = "Alternative ID selector"
                        EstimatedSuccessRate = Some 0.85
                    }
                    ShouldSucceedAfterHealing = true
                    MaxHealsToTest = 2
                }
                {
                    Name = "MaxHealsExceeded"
                    Description = "Test that max heals limit is respected"
                    Platform = "web"
                    OriginalAction = TypeText ("#failing-input", "test")
                    ExpectedFailure = "element not found"
                    ExpectedHealingSolution = None
                    ShouldSucceedAfterHealing = false
                    MaxHealsToTest = 1
                }
            ]
            
            /// Run single test scenario
            member _.RunTestScenario(scenario: TestScenario) =
                async {
                    let startTime = DateTimeOffset.Now
                    
                    try
                        // Create mock components
                        let mockAI = new MockAIProvider(Map.empty)
                        let mockExecutor = new MockActionExecutor(
                            (fun action -> action = scenario.OriginalAction),
                            (fun action -> scenario.ShouldSucceedAfterHealing && action <> scenario.OriginalAction)
                        )
                        
                        // Create healing manager with test configuration
                        let testConfig = { defaultAutoHealConfig with MaxHealAttempts = scenario.MaxHealsToTest }
                        let healingManager = new AutoHealManager(testConfig, Some mockAI, None)
                        
                        // Create healing context
                        let context = HealingRetryLogic.createHealingContext
                                        scenario.OriginalAction
                                        mockExecutor.Execute
                                        healingManager
                                        testConfig
                                        scenario.Platform
                        
                        // Execute with healing
                        let! result = HealingRetryLogic.executeWithHealingRetry context
                        let endTime = DateTimeOffset.Now
                        
                        let testResult = {
                            Scenario = scenario
                            Success = 
                                match result with
                                | HealingRetryLogic.RetrySuccess _ -> scenario.ShouldSucceedAfterHealing
                                | HealingRetryLogic.MaxHealsExceeded _ -> not scenario.ShouldSucceedAfterHealing
                                | _ -> false
                            ActualResult = result.ToString()
                            HealingAttempts = 
                                match result with
                                | HealingRetryLogic.RetrySuccess (_, attempts, _) -> attempts
                                | HealingRetryLogic.RetryFailed (_, attempts, _) -> attempts
                                | HealingRetryLogic.MaxHealsExceeded attempts -> attempts
                                | _ -> 0
                            HealingSolutions = []
                            ExecutionTime = endTime - startTime
                            ErrorMessages = []
                        }
                        
                        return testResult
                        
                    with
                    | ex ->
                        let endTime = DateTimeOffset.Now
                        return {
                            Scenario = scenario
                            Success = false
                            ActualResult = $"Exception: {ex.Message}"
                            HealingAttempts = 0
                            HealingSolutions = []
                            ExecutionTime = endTime - startTime
                            ErrorMessages = [ex.Message]
                        }
                }
            
            /// Run full test suite
            member this.RunFullTestSuite() =
                async {
                    let scenarios = this.GenerateTestScenarios()
                    let! results = 
                        scenarios 
                        |> List.map (this.RunTestScenario) 
                        |> Async.Parallel
                    
                    let totalTests = results.Length
                    let passedTests = results |> Array.filter (fun r -> r.Success) |> Array.length
                    let failedTests = totalTests - passedTests
                    
                    return {|
                        TotalTests = totalTests
                        PassedTests = passedTests
                        FailedTests = failedTests
                        PassRate = if totalTests > 0 then float passedTests / float totalTests else 0.0
                        Results = results |> Array.toList
                        Summary = $"Passed: {passedTests}/{totalTests} ({float passedTests / float totalTests * 100.0:F1}%)"
                    |}
                }
